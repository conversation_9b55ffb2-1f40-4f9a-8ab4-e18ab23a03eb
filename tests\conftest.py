import pytest
import os
import base64
from fastapi.testclient import TestClient
from app.main import app

@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app)

@pytest.fixture
def sample_image_bytes():
    """从data目录读取真实的测试图像"""
    image_path = os.path.join(os.path.dirname(__file__), "..", "data", "待矫正图_01.jpg")
    with open(image_path, "rb") as f:
        return f.read()

@pytest.fixture
def sample_image_base64():
    """返回真实测试图像的base64编码"""
    image_path = os.path.join(os.path.dirname(__file__), "..", "data", "待矫正图_01.jpg")
    with open(image_path, "rb") as f:
        image_bytes = f.read()
    return base64.b64encode(image_bytes).decode('ascii')
