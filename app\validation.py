from typing import Optional
from fastapi import UploadFile
from app.config import settings
from app.exceptions import *

def validate_uploaded_file(image: UploadFile) -> None:
    """验证上传的文件"""
    if not image:
        return
        
    # 检查文件大小
    if hasattr(image, 'size') and image.size > settings.max_image_size:
        raise ImageTooLargeError(f"Image size {image.size} exceeds limit {settings.max_image_size}")
    
    # 检查文件类型
    if image.content_type:
        if not image.content_type.startswith('image/'):
            raise UnsupportedFormatError(f"Content type {image.content_type} not supported")
        
        format_name = image.content_type.split('/')[-1].lower()
        if format_name not in settings.supported_formats_list:
            raise UnsupportedFormatError(f"Format {format_name} not supported")

def validate_base64_image(image_base64: Optional[str]) -> None:
    """验证base64图像"""
    if not image_base64:
        return
        
    import base64
    
    try:
        # 尝试解码
        if ',' in image_base64:
            image_base64 = image_base64.split(',')[1]
        
        decoded = base64.b64decode(image_base64)
        
        # 检查大小
        if len(decoded) > settings.max_image_size:
            raise ImageTooLargeError(f"Decoded image size exceeds limit")
            
    except Exception as e:
        raise InvalidImageError(f"Invalid base64 image: {e}")

def validate_image_url(image_url: Optional[str]) -> None:
    """验证图像URL"""
    if not image_url:
        return
        
    import re
    
    # 基础URL格式检查
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    if not url_pattern.match(image_url):
        raise InvalidImageError("Invalid URL format")
