#!/usr/bin/env python3
"""
API 图像矫正测试脚本
调用运行中的 FastAPI 服务来矫正 data/ 目录下的图片
"""

import os
import base64
import requests
import json
from pathlib import Path
import time

# API 配置
API_BASE_URL = "http://localhost:8000"
RECTIFY_ENDPOINT = f"{API_BASE_URL}/v1/rectify"
HEALTH_ENDPOINT = f"{API_BASE_URL}/v1/health"

def check_service_health():
    """检查服务是否正常运行"""
    try:
        response = requests.get(HEALTH_ENDPOINT, timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务健康状态: {data}")
            return True
        else:
            print(f"❌ 服务健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        print("请确保 FastAPI 服务正在运行: uvicorn app.main:app --reload --port 8000")
        return False

def test_with_file_upload(image_path: Path, output_dir: Path):
    """使用文件上传方式测试"""
    print(f"\n📤 测试文件上传: {image_path.name}")
    
    try:
        with open(image_path, 'rb') as f:
            files = {'image': (image_path.name, f, 'image/jpeg')}
            
            start_time = time.time()
            response = requests.post(RECTIFY_ENDPOINT, files=files, timeout=30)
            end_time = time.time()
            
        print(f"   📊 响应状态: {response.status_code}")
        print(f"   ⏱️  请求耗时: {(end_time - start_time)*1000:.1f}ms")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   🆔 图片ID: {data.get('id', 'N/A')}")
            print(f"   📍 检测分数: {data.get('score', 'N/A')}")
            print(f"   📐 图片尺寸: {data.get('size', {}).get('w', 'N/A')}x{data.get('size', {}).get('h', 'N/A')}")
            
            if 'timings' in data and data['timings']:
                timings = data['timings']
                print(f"   ⏱️  服务器处理时间: {timings.get('total_ms', 'N/A')}ms")
                print(f"      - 检测: {timings.get('detect_ms', 'N/A')}ms")
                print(f"      - 增强: {timings.get('enhance_ms', 'N/A')}ms")
            
            # 保存矫正后的图片
            if 'rectified_image' in data and data['rectified_image']:
                output_file = output_dir / f"api_rectified_{image_path.stem}.jpg"
                try:
                    image_data = base64.b64decode(data['rectified_image'])
                    with open(output_file, 'wb') as f:
                        f.write(image_data)
                    print(f"   ✅ 矫正图片已保存: {output_file}")
                    return True
                except Exception as e:
                    print(f"   ❌ 保存图片失败: {e}")
                    return False
            else:
                print(f"   ⚠️  响应中没有矫正图片数据")
                return False
        else:
            print(f"   ❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def test_with_base64(image_path: Path, output_dir: Path):
    """使用 base64 方式测试"""
    print(f"\n📤 测试 Base64 上传: {image_path.name}")
    
    try:
        # 读取图片并转换为 base64
        with open(image_path, 'rb') as f:
            image_bytes = f.read()
        
        image_base64 = base64.b64encode(image_bytes).decode('ascii')
        
        payload = {
            "image_base64": f"data:image/jpeg;base64,{image_base64}",
            "options": {
                "enhance": {
                    "brighten": True,
                    "denoise": True,
                    "sharpen": False,
                    "binarize": False,
                    "grayscale": False
                },
                "outputs": {
                    "crop": True,
                    "rectified": True,
                    "debug": False
                },
                "return_points": True
            }
        }
        
        start_time = time.time()
        response = requests.post(RECTIFY_ENDPOINT, json=payload, timeout=30)
        end_time = time.time()
        
        print(f"   📊 响应状态: {response.status_code}")
        print(f"   ⏱️  请求耗时: {(end_time - start_time)*1000:.1f}ms")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   🆔 图片ID: {data.get('id', 'N/A')}")
            print(f"   📍 检测分数: {data.get('score', 'N/A')}")
            print(f"   📐 图片尺寸: {data.get('size', {}).get('w', 'N/A')}x{data.get('size', {}).get('h', 'N/A')}")
            
            if 'points' in data and data['points']:
                points = data['points']
                print(f"   📍 检测到的角点: {len(points)}个")
                for i, (x, y) in enumerate(points):
                    print(f"      点{i+1}: ({x:.1f}, {y:.1f})")
            
            # 保存矫正后的图片
            if 'rectified_image' in data and data['rectified_image']:
                output_file = output_dir / f"api_base64_{image_path.stem}.jpg"
                try:
                    image_data = base64.b64decode(data['rectified_image'])
                    with open(output_file, 'wb') as f:
                        f.write(image_data)
                    print(f"   ✅ 矫正图片已保存: {output_file}")
                    return True
                except Exception as e:
                    print(f"   ❌ 保存图片失败: {e}")
                    return False
            else:
                print(f"   ⚠️  响应中没有矫正图片数据")
                return False
        else:
            print(f"   ❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始 API 图像矫正测试...")
    
    # 检查服务健康状态
    if not check_service_health():
        return
    
    # 检查 data 目录
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ data/ 目录不存在")
        return
    
    # 查找图片文件
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    image_files = [f for f in data_dir.iterdir() 
                   if f.is_file() and f.suffix.lower() in image_extensions]
    
    if not image_files:
        print("❌ data/ 目录中没有找到图片文件")
        return
    
    print(f"📁 找到 {len(image_files)} 张图片")
    
    # 创建输出目录
    output_dir = Path("output_api_test")
    output_dir.mkdir(exist_ok=True)
    print(f"📂 输出目录: {output_dir.absolute()}")
    
    # 测试统计
    total_tests = 0
    successful_tests = 0
    
    # 对每张图片进行测试
    for image_file in image_files:
        print(f"\n{'='*60}")
        print(f"📸 处理图片: {image_file.name}")
        print(f"{'='*60}")
        
        # 测试1: 文件上传方式
        total_tests += 1
        if test_with_file_upload(image_file, output_dir):
            successful_tests += 1
        
        # 测试2: Base64 方式
        total_tests += 1
        if test_with_base64(image_file, output_dir):
            successful_tests += 1
        
        # 添加延迟避免过快请求
        time.sleep(0.5)
    
    # 输出测试结果
    print(f"\n{'='*60}")
    print(f"🎉 测试完成！")
    print(f"📊 成功率: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"📂 输出目录: {output_dir.absolute()}")
    print(f"💡 提示: 查看 {output_dir} 目录中的矫正结果")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
