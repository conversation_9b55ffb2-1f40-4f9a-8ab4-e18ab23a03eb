# MVP需求定义文档

## 文档说明

基于原PRD文档的现实审视，重新定义各个阶段的MVP需求。原PRD野心过大，这里按照"最小可行产品"的原则，分阶段定义明确的功能边界。

## Phase 0: 修复现状 - 不是MVP，是预备工作

### 核心目标
让现有代码真正可运行、可维护、可部署

### 功能范围
- **什么要做**：基础工程化改造
- **什么不做**：新功能开发

### 具体需求

#### 依赖管理
- 创建 `requirements.txt`，锁定所有依赖版本
- 支持 `pip install -r requirements.txt` 一键安装

#### 配置管理
- 移除所有硬编码值（特别是 `"img_mvp"`, `"req_mvp"`）
- 支持环境变量配置
- 添加配置文件支持（.env）

#### 基础错误处理
- 统一异常处理机制
- 返回有意义的错误信息
- 基础的输入验证

#### 部署支持
- 提供运行说明文档
- Docker支持（Dockerfile）

### 验收标准
- [ ] 新环境可以通过文档快速启动服务
- [ ] 没有硬编码的魔术值
- [ ] API调用有合理的错误返回
- [ ] 代码可以通过基础的语法检查

---

## Phase 1: 真正的MVP - 核心单图处理

### 核心目标
提供稳定可靠的单张图片处理服务

### 功能边界
- **包含**：同步单图处理、基础认证、错误处理、日志
- **不包含**：异步处理、批量处理、高级算法

### 具体需求

#### 核心功能
1. **图像输入处理**
   - 支持：multipart/form-data 文件上传
   - 支持：JSON中的base64编码
   - 支持：URL形式的图片链接
   - 限制：最大10MB，JPEG/PNG格式

2. **文档检测与矫正**
   - 自动检测文档四角
   - 透视变换矫正
   - 基础的图像增强（亮度、对比度）
   - 返回矫正后的base64图像

3. **API接口**
   - `POST /v1/rectify` - 单图处理
   - `GET /v1/health` - 健康检查（真实检查）
   - 统一的错误响应格式

#### 质量要求
1. **性能指标**
   - 单次处理时间 < 2秒（1080p图片）
   - 支持10并发请求无错误
   - 内存使用 < 512MB（单实例）

2. **可靠性**
   - 服务启动成功率 100%
   - 正常图片处理成功率 > 95%
   - 异常输入有友好错误提示

3. **安全性**
   - API Key认证机制
   - 基础的输入验证和清理
   - 文件大小和格式限制

#### 运维支持
1. **日志系统**
   - 结构化JSON日志
   - 包含request_id追踪
   - 记录处理时间和结果

2. **监控**
   - 健康检查端点实际检查服务状态
   - 基础的性能指标收集

3. **部署**
   - Docker容器化
   - 简单的部署文档
   - 环境配置说明

### 验收标准
- [ ] 通过Postman可以稳定调用所有API
- [ ] 处理标准测试图片成功率100%
- [ ] 服务可以连续运行24小时无崩溃
- [ ] 有完整的API使用文档
- [ ] Docker部署一次成功

### 明确不包含的功能
- 异步作业处理
- 批量图片处理
- 高级弯曲矫正算法
- 用户管理
- 配额限制
- 回调通知

---

## Phase 2: 生产就绪版本

### 核心目标
让MVP能够在生产环境稳定运行

### 新增功能

#### 生产级特性
1. **配置管理**
   - 支持多环境配置（dev/test/prod）
   - 配置热更新（部分参数）
   - 敏感信息安全存储

2. **幂等性**
   - 支持Idempotency-Key
   - 避免重复处理

3. **监控和指标**
   - Prometheus格式的metrics端点
   - 详细的性能指标
   - 错误率统计

4. **限流和保护**
   - 基于内存的简单限流
   - 超时保护
   - 优雅关闭

#### 增强功能
1. **更好的错误处理**
   - 完整的错误码体系
   - 多语言错误信息
   - 详细的错误上下文

2. **性能优化**
   - 结果缓存（内存缓存）
   - 图像处理优化
   - 并发处理改进

### 性能指标
- P95延迟 < 800ms（符合原PRD要求）
- 支持50并发无错误
- 内存使用稳定，无泄漏
- 错误率 < 0.1%

### 验收标准
- [ ] 通过压力测试（50并发 * 10分钟）
- [ ] 监控指标完整且准确
- [ ] 有运维手册和故障排查指南
- [ ] 支持零停机更新

---

## Phase 3: 异步处理版本

### 核心目标
支持批量和大文件处理

### 新增功能

#### 异步作业系统
1. **作业管理**
   - 内存队列（避免外部依赖）
   - 作业状态跟踪
   - 结果存储和查询

2. **API扩展**
   - `POST /v1/jobs` - 创建批处理作业
   - `GET /v1/jobs/{job_id}` - 查询作业状态
   - 支持单次提交最多50张图片

3. **可靠性保证**
   - 作业重试机制
   - 超时处理
   - 失败作业清理

### 性能指标
- 单作业处理100张图片
- 队列处理能力 > 20 QPS
- 作业完成率 > 99%

### 验收标准
- [ ] 批量处理功能正常
- [ ] 作业状态准确跟踪
- [ ] 系统资源使用合理

---

## Phase 4: 算法增强版本

### 核心目标
提升图像处理质量和鲁棒性

### 改进功能
1. **更好的文档检测**
   - 多策略检测算法
   - 复杂背景适应
   - 检测质量评分

2. **弯曲矫正**
   - 基础的弯曲检测和矫正
   - 多档质量设置
   - 处理失败降级策略

### 验收标准
- [ ] 文档检测准确率 > 95%
- [ ] 处理失败率 < 1%
- [ ] 有算法质量评估报告

---

## 总体原则

### 开发原则
1. **简单优先**：能用简单方案就不用复杂方案
2. **功能边界清晰**：明确什么做什么不做
3. **可测试**：每个功能都要有验证方法
4. **可观测**：关键操作都要有日志和指标

### 质量原则
1. **稳定性 > 功能丰富度**
2. **可维护性 > 代码简洁度**
3. **用户体验 > 技术炫技**

### 交付原则
1. **小步快跑**：每个阶段都交付可用价值
2. **持续改进**：基于反馈迭代
3. **文档同步**：代码和文档同步更新

## 风险控制

### 每阶段必须控制的风险
1. **技术风险**：依赖库兼容性、性能瓶颈
2. **质量风险**：测试覆盖不足、边界情况未考虑
3. **交付风险**：范围蔓延、时间延期

### 风险缓解措施
1. **技术选型保守**：选择成熟稳定的技术栈
2. **测试先行**：关键功能先写测试再写实现
3. **范围控制**：严格按照文档执行，拒绝范围扩大