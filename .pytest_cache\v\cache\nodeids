["tests/test_api_endpoints.py::TestErrorHandling::test_jobs_empty_items_list", "tests/test_api_endpoints.py::TestErrorHandling::test_rectify_invalid_base64", "tests/test_api_endpoints.py::TestErrorHandling::test_rectify_no_image_provided", "tests/test_api_endpoints.py::TestHealthEndpoint::test_health_endpoint_no_auth_required", "tests/test_api_endpoints.py::TestHealthEndpoint::test_health_returns_ok_status", "tests/test_api_endpoints.py::TestJobsEndpoint::test_create_job_with_valid_items", "tests/test_api_endpoints.py::TestJobsEndpoint::test_get_job_status", "tests/test_api_endpoints.py::TestRectifyEndpoint::test_rectify_with_base64_image", "tests/test_api_endpoints.py::TestRectifyEndpoint::test_rectify_with_different_enhance_options", "tests/test_api_endpoints.py::TestRectifyEndpoint::test_rectify_with_valid_image_file", "tests/test_api_endpoints.py::TestRequestIdTracking::test_idempotency_key_handling", "tests/test_api_endpoints.py::TestRequestIdTracking::test_request_id_in_response", "tests/test_performance.py::TestMemoryUsage::test_multiple_sequential_requests", "tests/test_performance.py::TestPerformance::test_concurrent_requests", "tests/test_performance.py::TestPerformance::test_health_endpoint_performance", "tests/test_performance.py::TestPerformance::test_single_request_performance", "tests/test_performance.py::TestScalability::test_stress_test", "tests/test_smoke.py::test_api_consistency", "tests/test_smoke.py::test_health_endpoint", "tests/test_smoke.py::test_jobs_endpoint_basic", "tests/test_smoke.py::test_rectify_endpoint_no_image", "tests/test_smoke.py::test_rectify_endpoint_with_base64", "tests/test_smoke.py::test_rectify_endpoint_with_file"]