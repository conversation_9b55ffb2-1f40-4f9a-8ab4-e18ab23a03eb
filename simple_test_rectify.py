#!/usr/bin/env python3
"""
简单的图像矫正测试脚本
调用 FastAPI 服务矫正图片并显示详细结果
"""

import os
import base64
import requests
import json
from pathlib import Path
import time

# API 配置
API_BASE_URL = "http://localhost:8000"
RECTIFY_ENDPOINT = f"{API_BASE_URL}/v1/rectify"

def test_image_rectification(image_path: Path, output_dir: Path):
    """测试图像矫正"""
    print(f"\n📸 处理图片: {image_path.name}")
    print(f"   📁 原图路径: {image_path}")
    
    try:
        # 使用文件上传方式
        with open(image_path, 'rb') as f:
            files = {'image': (image_path.name, f, 'image/jpeg')}
            
            # 添加处理选项
            data = {
                'options': json.dumps({
                    "enhance": {
                        "brighten": True,
                        "denoise": True,
                        "sharpen": True,
                        "binarize": False,
                        "grayscale": False
                    },
                    "auto_deskew": True,
                    "deskew_max_angle": 25.0,
                    "return_points": True
                })
            }
            
            print(f"   🚀 发送请求...")
            start_time = time.time()
            response = requests.post(RECTIFY_ENDPOINT, files=files, data=data, timeout=30)
            end_time = time.time()
            
        print(f"   📊 响应状态: {response.status_code}")
        print(f"   ⏱️  总耗时: {(end_time - start_time)*1000:.1f}ms")
        
        if response.status_code == 200:
            data = response.json()
            
            # 显示详细信息
            print(f"   🆔 图片ID: {data.get('id', 'N/A')}")
            print(f"   📍 检测分数: {data.get('score', 'N/A')}")
            print(f"   🔄 旋转角度: {data.get('angle', 'N/A')}°")
            
            size = data.get('size', {})
            print(f"   📐 图片尺寸: {size.get('w', 'N/A')}x{size.get('h', 'N/A')}")
            
            # 显示处理时间
            if 'timings' in data and data['timings']:
                timings = data['timings']
                print(f"   ⏱️  服务器处理时间:")
                print(f"      - 总计: {timings.get('total_ms', 'N/A')}ms")
                print(f"      - 检测: {timings.get('detect_ms', 'N/A')}ms")
                print(f"      - 增强: {timings.get('enhance_ms', 'N/A')}ms")
            
            # 显示检测到的角点
            if 'points' in data and data['points']:
                points = data['points']
                print(f"   📍 检测到的角点: {len(points)}个")
                for i, (x, y) in enumerate(points):
                    print(f"      点{i+1}: ({x:.1f}, {y:.1f})")
            
            # 保存矫正后的图片
            if 'rectified_image' in data and data['rectified_image']:
                output_file = output_dir / f"rectified_{image_path.stem}.jpg"
                try:
                    image_data = base64.b64decode(data['rectified_image'])
                    with open(output_file, 'wb') as f:
                        f.write(image_data)
                    print(f"   ✅ 矫正图片已保存: {output_file}")
                    print(f"   📊 矫正图片大小: {len(image_data)} 字节")
                    return True
                except Exception as e:
                    print(f"   ❌ 保存图片失败: {e}")
                    return False
            else:
                print(f"   ⚠️  响应中没有矫正图片数据")
                print(f"   📄 完整响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                return False
        else:
            print(f"   ❌ 请求失败 ({response.status_code}): {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 图像矫正 API 测试")
    print("="*50)
    
    # 检查服务健康状态
    try:
        health_response = requests.get(f"{API_BASE_URL}/v1/health", timeout=5)
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"✅ 服务状态: {health_data['status']}")
            print(f"📦 版本: {health_data['version']}")
        else:
            print(f"❌ 服务健康检查失败: {health_response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        print("请确保 FastAPI 服务正在运行在 http://localhost:8000")
        return
    
    # 检查 data 目录
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ data/ 目录不存在")
        return
    
    # 查找图片文件
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    image_files = [f for f in data_dir.iterdir() 
                   if f.is_file() and f.suffix.lower() in image_extensions]
    
    if not image_files:
        print("❌ data/ 目录中没有找到图片文件")
        return
    
    print(f"📁 找到 {len(image_files)} 张图片")
    
    # 创建输出目录
    output_dir = Path("output_simple_test")
    output_dir.mkdir(exist_ok=True)
    print(f"📂 输出目录: {output_dir.absolute()}")
    
    # 处理每张图片
    successful_count = 0
    for i, image_file in enumerate(image_files, 1):
        print(f"\n{'='*50}")
        print(f"📸 [{i}/{len(image_files)}] {image_file.name}")
        print(f"{'='*50}")
        
        if test_image_rectification(image_file, output_dir):
            successful_count += 1
        
        # 添加延迟
        if i < len(image_files):
            time.sleep(1)
    
    # 输出结果
    print(f"\n{'='*50}")
    print(f"🎉 处理完成！")
    print(f"📊 成功: {successful_count}/{len(image_files)} 张图片")
    print(f"📂 查看结果: {output_dir.absolute()}")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
