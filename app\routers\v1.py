from fastapi import APIRout<PERSON>, Header, HTTPException, UploadFile, File, Form
from pydantic import BaseModel, Field
from typing import List, Optional, Literal, Tuple
import uuid
import logging

from app.pipeline.traditional import run_pipeline, PipelineOptions
from app.logging import generate_request_id, set_request_id
from app.validation import validate_uploaded_file, validate_base64_image, validate_image_url
from app.exceptions import InvalidImageError, ImageTooLargeError, UnsupportedFormatError, ProcessingTimeoutError

logger = logging.getLogger(__name__)
router = APIRouter()

# ---- Models (subset aligned with OpenAPI) ----
Point = Tuple[float, float]

class Size(BaseModel):
    w: int
    h: int

class Timings(BaseModel):
    detect_ms: Optional[float] = None
    dewarp_ms: Optional[float] = None
    enhance_ms: Optional[float] = None
    total_ms: Optional[float] = None

class EnhanceOptions(BaseModel):
    brighten: Optional[bool] = None
    grayscale: Optional[bool] = None
    binarize: Optional[bool] = None
    denoise: Optional[bool] = None
    sharpen: Optional[bool] = None

class OutputOptions(BaseModel):
    crop: Optional[bool] = None
    rectified: Optional[bool] = None
    mask: Optional[bool] = None
    debug: Optional[bool] = None

class Options(BaseModel):
    mode: Optional[Literal['fast', 'balanced', 'hq']] = Field('balanced')
    enhance: Optional[EnhanceOptions] = None
    outputs: Optional[OutputOptions] = None
    return_points: Optional[bool] = None
    return_mesh: Optional[bool] = None

class RectifyJSONRequest(BaseModel):
    image_base64: Optional[str] = None
    image_url: Optional[str] = None
    options: Optional[Options] = None

class RectifyResult(BaseModel):
    id: Optional[str] = None
    points: Optional[List[Point]] = None
    score: Optional[float] = None
    angle: Optional[float] = None
    size: Optional[Size] = None
    crop_image: Optional[str] = None
    rectified_image: Optional[str] = None
    mesh: Optional[dict] = None
    timings: Optional[Timings] = None
    request_id: Optional[str] = None

class JobItem(BaseModel):
    id: Optional[str] = None
    image_base64: Optional[str] = None
    image_url: Optional[str] = None
    options: Optional[Options] = None

class JobCreateRequest(BaseModel):
    items: List[JobItem] = Field(..., min_length=1)  # 至少需要一个项目
    callback_url: Optional[str] = None

class JobCreateResponse(BaseModel):
    job_id: str
    queued: bool

class JobStatusResponse(BaseModel):
    status: Literal['queued', 'processing', 'done', 'error']
    results: Optional[List[RectifyResult]] = None
    errors: Optional[List[dict]] = None

# ---- Routes ----
@router.post('/rectify', response_model=RectifyResult, tags=["rectify"])
async def rectify(
    payload: Optional[RectifyJSONRequest] = None,
    image: Optional[UploadFile] = File(default=None),
    options: Optional[str] = Form(default=None),  # JSON字符串形式的选项
    idempotency_key: Optional[str] = Header(default=None, alias="Idempotency-Key"),
):
    # 生成并设置request_id
    request_id = generate_request_id()
    set_request_id(request_id)

    logger.info("Processing rectify request", extra={
        "request_id": request_id,
        "has_image": image is not None,
        "has_payload": payload is not None,
        "idempotency_key": idempotency_key
    })

    try:
        # 输入验证
        validate_uploaded_file(image)
        if payload:
            validate_base64_image(payload.image_base64)
            validate_image_url(payload.image_url)

        # 检查是否提供了图像
        if not image and not (payload and (payload.image_base64 or payload.image_url)):
            raise InvalidImageError("No image provided")

        # 记录处理开始
        logger.info("Starting image processing")

        # 准备处理参数
        image_bytes = None
        image_b64 = None
        image_url = None
        pipeline_options = PipelineOptions()

        # 处理JSON payload中的选项
        if payload and payload.options and payload.options.enhance:
            pipeline_options.binarize = bool(payload.options.enhance.binarize)
            pipeline_options.brighten = bool(payload.options.enhance.brighten)
            pipeline_options.grayscale = bool(payload.options.enhance.grayscale)
            pipeline_options.denoise = bool(payload.options.enhance.denoise)
            pipeline_options.sharpen = bool(payload.options.enhance.sharpen)

        # 处理文件上传时的选项参数
        if options:
            try:
                import json
                opts_dict = json.loads(options)
                logger.info(f"Parsed options: {opts_dict}")

                # 处理enhance选项
                if 'enhance' in opts_dict:
                    enhance = opts_dict['enhance']
                    pipeline_options.binarize = bool(enhance.get('binarize', pipeline_options.binarize))
                    pipeline_options.brighten = bool(enhance.get('brighten', pipeline_options.brighten))
                    pipeline_options.grayscale = bool(enhance.get('grayscale', pipeline_options.grayscale))
                    pipeline_options.denoise = bool(enhance.get('denoise', pipeline_options.denoise))
                    pipeline_options.sharpen = bool(enhance.get('sharpen', pipeline_options.sharpen))

                # 处理去倾斜选项
                if 'auto_deskew' in opts_dict:
                    pipeline_options.auto_deskew = bool(opts_dict['auto_deskew'])
                    logger.info(f"Set auto_deskew to: {pipeline_options.auto_deskew}")
                if 'deskew_max_angle' in opts_dict:
                    pipeline_options.deskew_max_angle = float(opts_dict['deskew_max_angle'])
                    logger.info(f"Set deskew_max_angle to: {pipeline_options.deskew_max_angle}")

            except (json.JSONDecodeError, ValueError, TypeError) as e:
                logger.warning(f"Invalid options parameter: {e}")
                # 继续使用默认选项

        logger.info(f"Final pipeline options: auto_deskew={pipeline_options.auto_deskew}, deskew_max_angle={pipeline_options.deskew_max_angle}")

        if image is not None:
            image_bytes = await image.read()
        if payload:
            image_b64 = payload.image_base64
            image_url = payload.image_url

        result = run_pipeline(image_bytes=image_bytes, image_base64=image_b64, image_url=image_url, options=pipeline_options)

        # 记录处理成功
        logger.info("Image processing completed successfully", extra={
            "processing_time_ms": result.timings.get('total_ms'),
            "image_size": result.size,
            "detection_score": result.score
        })

        return RectifyResult(
            id=f"img_{uuid.uuid4().hex[:8]}",
            points=[(float(x), float(y)) for (x, y) in result.points],
            score=result.score,
            angle=result.angle,
            size=Size(w=result.size[0], h=result.size[1]),
            rectified_image=result.rectified_b64,
            timings=Timings(
                detect_ms=result.timings.get('detect_ms'),
                enhance_ms=result.timings.get('post_ms'),
                total_ms=result.timings.get('total_ms'),
            ),
            request_id=request_id,
        )

    except (InvalidImageError, ImageTooLargeError, UnsupportedFormatError) as e:
        logger.warning("Client error in image processing", extra={"error": str(e)})
        raise HTTPException(status_code=400, detail=str(e))
    except ProcessingTimeoutError as e:
        logger.error("Processing timeout", extra={"error": str(e)})
        raise HTTPException(status_code=504, detail="Processing timeout")
    except Exception as e:
        logger.error("Unexpected error in image processing", extra={"error": str(e)})
        raise HTTPException(status_code=500, detail="Internal processing error")

@router.post('/jobs', response_model=JobCreateResponse, status_code=202, tags=["jobs"])
async def create_job(
    payload: JobCreateRequest,
    idempotency_key: Optional[str] = Header(default=None, alias="Idempotency-Key"),
):
    request_id = generate_request_id()
    set_request_id(request_id)

    logger.info("Creating async job", extra={
        "request_id": request_id,
        "item_count": len(payload.items),
        "idempotency_key": idempotency_key
    })

    # TODO: enqueue items to background workers
    job_id = f"job_{uuid.uuid4()}"
    logger.info("Job created", extra={"job_id": job_id})

    return JobCreateResponse(job_id=job_id, queued=True)

@router.get('/jobs/{job_id}', response_model=JobStatusResponse, tags=["jobs"])
async def get_job(job_id: str):
    # TODO: query job store and return status/results
    return JobStatusResponse(status='queued')

@router.get('/quota', tags=["system"])
async def quota():
    # TODO: implement tenant/user quota lookup
    return {"limit": 10000, "used": 0, "reset_at": None}

