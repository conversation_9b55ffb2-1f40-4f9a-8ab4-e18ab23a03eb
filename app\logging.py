import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Optional
from contextvars import ContextVar

# 全局的request_id上下文变量
request_id_var: ContextVar[Optional[str]] = ContextVar('request_id', default=None)

class JSONFormatter(logging.Formatter):
    """JSON格式的日志格式化器"""
    
    def format(self, record):
        log_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "request_id": request_id_var.get(),
        }
        
        # 添加额外的字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)

        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)

        return json.dumps(log_entry, ensure_ascii=False)

def setup_logging(log_level: str = "INFO", log_format: str = "json"):
    """设置日志系统"""
    level = getattr(logging, log_level.upper())
    
    # 清除现有的handlers
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    handler = logging.StreamHandler()
    
    if log_format.lower() == "json":
        handler.setFormatter(JSONFormatter())
    else:
        handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
    
    root_logger.addHandler(handler)
    root_logger.setLevel(level)
    
    return root_logger

def generate_request_id() -> str:
    """生成请求ID"""
    return f"req_{uuid.uuid4().hex[:8]}"

def set_request_id(request_id: str):
    """设置当前请求的ID"""
    request_id_var.set(request_id)

def get_request_id() -> Optional[str]:
    """获取当前请求的ID"""
    return request_id_var.get()

def log_with_extra(logger, level: str, message: str, **extra_fields):
    """记录带有额外字段的日志"""
    record = logging.LogRecord(
        name=logger.name,
        level=getattr(logging, level.upper()),
        pathname="",
        lineno=0,
        msg=message,
        args=(),
        exc_info=None
    )
    record.extra_fields = extra_fields
    logger.handle(record)
