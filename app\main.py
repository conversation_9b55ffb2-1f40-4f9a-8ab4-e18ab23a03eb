from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from starlette.responses import JSONResponse
import logging

from app.routers.v1 import router as api_v1
from app.config import settings
from app.logging import setup_logging

# 设置日志系统
setup_logging(settings.log_level, settings.log_format)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.api_title,
    version=settings.api_version
)

# 配置化的CORS设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins_list,
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["Authorization", "Content-Type", "Idempotency-Key"],
)

@app.get("/v1/health", tags=["system"], include_in_schema=False)
async def health():
    # 真实的健康检查
    try:
        # 检查配置
        config_ok = bool(settings.api_title)

        # 检查依赖模块
        import cv2
        import numpy as np
        modules_ok = True

        return {
            "status": "ok" if (config_ok and modules_ok) else "degraded",
            "version": settings.api_version,
            "config_loaded": config_ok,
            "modules_loaded": modules_ok
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {"status": "error", "error": str(e)}

app.include_router(api_v1, prefix="/v1")

@app.exception_handler(Exception)
async def unhandled_exception_handler(request, exc):
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "code": "INTERNAL_ERROR",
            "message": "An unexpected error occurred"
        }
    )

