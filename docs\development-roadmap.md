# 图像文档矫正API - 重新规划的开发路线图

## 现状分析

当前代码是一个基础的技术验证Demo，距离PRD要求的生产级服务还有很大差距。主要问题：

1. **功能完整性不足**：异步作业系统仅有空壳实现
2. **生产级特性缺失**：无认证、监控、错误处理、配置管理
3. **代码质量问题**：硬编码值、缺乏验证、无测试覆盖
4. **架构设计不当**：PRD野心过大，MVP定义不清晰

## 分阶段MVP计划

### Phase 0: 立即修复 (2天)
**目标：让现有代码真正可运行、可调试、可测试**

#### 核心任务
- [ ] 创建 `requirements.txt` 文件，固化依赖版本（包含测试依赖）
- [ ] 添加 `.env` 配置文件支持
- [ ] 移除硬编码值（`"img_mvp"`, `"req_mvp"` 等）
- [ ] 添加基础的输入验证和错误处理
- [ ] 创建 `config.py` 配置管理模块
- [ ] **实现结构化日志系统（JSON格式，request_id追踪）**
- [ ] **添加冒烟测试（FastAPI TestClient，基础API验证）**
- [ ] 编写基础的部署文档

#### 新增的质量保证
- [ ] JSON格式的结构化日志，支持request_id链路追踪
- [ ] 冒烟测试覆盖核心端点，确保修复不破坏基本功能
- [ ] 测试可以检测硬编码值和基础错误处理
- [ ] 日志级别和格式可配置

#### 验收标准
- 服务可以通过 `pip install -r requirements.txt` 安装依赖
- 所有魔术数字和硬编码值被配置化
- API返回有意义的错误信息，有结构化日志记录
- 冒烟测试 `python tests/run_smoke.py` 通过
- 每个请求都有唯一的request_id，可追踪完整链路
- 有基础的部署说明文档

### Phase 1: 真正的MVP (3-5天)
**目标：一个稳定可用的单图处理服务**

#### 核心功能
- [ ] 完善图片输入验证（大小、格式、安全检查）
- [ ] 改进文档检测算法（添加fallback策略）
- [ ] 实现基础API认证（API Key机制）
- [ ] 实现请求限流（简单的内存限流器）
- [ ] 添加健康检查端点的实际检查逻辑
- [ ] 扩展测试覆盖（集成测试和单元测试）

#### 非功能性需求
- [ ] 单次请求处理时间 < 2秒（1080p图片）
- [ ] 支持JPEG/PNG格式，最大10MB
- [ ] 基础的并发处理（FastAPI默认）
- [ ] Docker容器化部署

#### 验收标准
- 通过Postman/curl可以稳定调用API
- 有完整的错误处理和日志记录
- 通过Docker可以一键部署
- 基础功能测试通过率100%

### Phase 2: 生产就绪 (1周)
**目标：满足基本生产环境要求**

#### 生产级特性
- [ ] 配置管理系统（环境变量 + YAML配置）
- [ ] 监控指标暴露（Prometheus格式的 `/metrics` 端点）
- [ ] 实现幂等性处理（Idempotency-Key）
- [ ] 完善错误码体系（参考PRD定义）
- [ ] 增强请求追踪（扩展request_id到更多组件）
- [ ] 基础缓存策略（内存缓存处理结果）
- [ ] 性能分析和优化

#### 运维支持
- [ ] 增强日志系统（支持ELK stack，添加更多业务指标）
- [ ] 优雅关闭和重启
- [ ] 资源限制和监控
- [ ] 压力测试报告

#### 验收标准
- P95延迟 < 800ms（符合PRD要求）
- 支持50并发无错误
- 完整的监控指标
- 部署文档和运维手册

### Phase 3: 异步处理 (1周)
**目标：支持批量和大文件处理**

#### 异步功能
- [ ] 实现内存队列（避免引入Redis复杂性）
- [ ] 作业状态管理（queued/processing/done/error）
- [ ] 作业查询和结果获取接口
- [ ] 基础重试机制（3次重试）
- [ ] 作业超时处理（可配置timeout）
- [ ] 批量处理优化

#### API实现
- [ ] `POST /v1/jobs` - 创建批处理作业
- [ ] `GET /v1/jobs/{job_id}` - 查询作业状态
- [ ] 支持作业取消和清理

#### 验收标准
- 支持单次提交100张图片
- 作业状态准确跟踪
- 失败作业有详细错误信息
- 内存使用控制在合理范围

### Phase 4: 算法增强 (2周)
**目标：提升图像处理质量**

#### 算法优化
- [ ] 多策略文档检测（边缘+轮廓+机器学习）
- [ ] 基础弯曲矫正算法
- [ ] 自适应参数调整
- [ ] 质量评分系统
- [ ] A/B测试框架支持

#### 新功能
- [ ] 多页文档检测
- [ ] 文档方向自动识别
- [ ] 高级图像增强选项
- [ ] 处理结果质量评估

#### 验收标准
- 文档检测准确率 > 95%（测试集）
- 处理失败率 < 1%
- 支持quality/speed模式切换

### Phase 5: 企业特性 (按需开发)
**目标：满足企业级需求**

#### 高级特性
- [ ] 多租户支持
- [ ] 配额管理系统
- [ ] 使用统计和计费
- [ ] 高级认证（JWT/OAuth2）
- [ ] 回调通知机制
- [ ] SLA监控和报警

#### 扩展性
- [ ] 水平扩展支持
- [ ] 外部存储集成（S3/OSS）
- [ ] 分布式队列（Redis/RabbitMQ）
- [ ] 微服务拆分准备

## 技术债务管理

### 立即处理（Phase 0）
1. **硬编码问题**：所有魔术数字和固定字符串
2. **错误处理**：统一异常处理机制
3. **依赖管理**：requirements.txt和版本锁定
4. **配置管理**：环境变量和配置文件

### 短期处理（Phase 1-2）
1. **测试覆盖**：单元测试和集成测试
2. **文档完善**：API文档和部署指南
3. **性能优化**：内存使用和处理速度
4. **安全加固**：输入验证和认证

### 长期规划（Phase 3+）
1. **架构重构**：微服务化准备
2. **算法升级**：ML模型集成
3. **运维自动化**：CI/CD流水线
4. **监控完善**：全链路追踪

## 风险管理

### 高风险项
1. **性能瓶颈**：图像处理CPU密集
   - 缓解：分层缓存、异步处理、参数调优
2. **算法准确性**：复杂场景检测失败
   - 缓解：多策略融合、人工审核接口、质量评分
3. **并发压力**：单机处理能力限制
   - 缓解：限流机制、队列缓冲、水平扩展

### 中风险项
1. **依赖管理**：OpenCV等库的版本兼容性
2. **内存泄漏**：长时间运行的稳定性
3. **配置复杂**：多环境部署的一致性

## 里程碑检查点

### 周检查点
- **Week 1**：Phase 0-1完成，基础MVP可用
- **Week 2**：Phase 2完成，生产就绪
- **Week 3**：Phase 3完成，异步处理可用
- **Week 4+**：Phase 4+按需推进

### 质量门禁
1. **代码质量**：测试覆盖率 > 80%
2. **性能指标**：符合PRD要求
3. **文档完善**：API文档、部署文档齐全
4. **安全检查**：基础安全扫描通过

## 团队协作建议

1. **每日同步**：进度、问题、风险
2. **代码评审**：所有提交必须经过review
3. **测试驱动**：先写测试，再写实现
4. **文档同步**：代码变更同步更新文档

## 成功标准

**Phase 1成功标准**：
- 能够稳定处理单张图片请求
- 有基础的错误处理和日志
- 可以Docker部署

**Phase 2成功标准**：
- 满足PRD的性能要求
- 有完整的监控和运维支持
- 通过压力测试

**Phase 3成功标准**：
- 支持批量异步处理
- 作业管理功能完整
- 系统稳定可靠

这个路线图的核心思想是"小步快跑，持续交付"，确保每个阶段都能交付可用的价值。