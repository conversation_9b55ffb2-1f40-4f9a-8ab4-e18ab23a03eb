# 立即修复指南

## 文档说明

这是一个可以立即执行的修复清单，解决当前代码的严重问题，让系统真正可运行。包含结构化日志和冒烟测试，确保修复质量。**预计总工作量：2天**

## 🚨 立即行动清单

### 1. 创建依赖管理文件

#### 创建 `requirements.txt`
```bash
# 在项目根目录执行
cat > requirements.txt << 'EOF'
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6
requests==2.31.0
numpy==1.24.4
opencv-python-headless==********
python-dotenv==1.0.0
pytest==7.4.3
httpx==0.25.2
EOF
```

#### 创建 `.env.example` 配置模板
```bash
cat > .env.example << 'EOF'
# 服务配置
API_HOST=0.0.0.0
API_PORT=8000
API_TITLE=Image Rectification API
API_VERSION=1.0.0

# 图像处理配置
MAX_IMAGE_SIZE=10485760
SUPPORTED_FORMATS=jpeg,jpg,png
PROCESSING_TIMEOUT=30

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# 算法参数
CANNY_LOW_THRESHOLD=50
CANNY_HIGH_THRESHOLD=150
CLAHE_CLIP_LIMIT=2.0
EOF
```

### 2. 创建配置管理模块

#### 创建 `app/config.py`
```python
import os
from typing import List
from pydantic import BaseSettings

class Settings(BaseSettings):
    # 服务配置
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_title: str = "Image Rectification API"
    api_version: str = "1.0.0"
    
    # 图像处理配置
    max_image_size: int = 10 * 1024 * 1024  # 10MB
    supported_formats: List[str] = ["jpeg", "jpg", "png"]
    processing_timeout: int = 30
    
    # 日志配置
    log_level: str = "INFO"
    log_format: str = "json"
    
    # 算法参数
    canny_low_threshold: int = 50
    canny_high_threshold: int = 150
    clahe_clip_limit: float = 2.0
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# 全局配置实例
settings = Settings()
```

### 3. 修复硬编码问题

#### 修改 `app/routers/v1.py`

**问题位置**：第105行、116行、134行

**原代码**：
```python
id="img_mvp",
request_id="req_mvp",
job_id="job_demo"
```

**修复代码**：
```python
import uuid
from app.config import settings

# 在rectify函数中
def generate_request_id() -> str:
    return f"req_{uuid.uuid4().hex[:8]}"

def generate_image_id() -> str:
    return f"img_{uuid.uuid4().hex[:8]}"

# 替换硬编码的地方
return RectifyResult(
    id=generate_image_id(),  # 替换 "img_mvp"
    # ... 其他字段
    request_id=generate_request_id(),  # 替换 "req_mvp"
)

# 在create_job函数中
return JobCreateResponse(
    job_id=f"job_{uuid.uuid4()}",  # 替换 "job_demo"
    queued=True
)
```

### 4. 添加基础错误处理

#### 创建 `app/exceptions.py`
```python
class ImageProcessingError(Exception):
    """图像处理基础异常"""
    pass

class InvalidImageError(ImageProcessingError):
    """无效图像异常"""
    pass

class ImageTooLargeError(ImageProcessingError):
    """图像过大异常"""
    pass

class UnsupportedFormatError(ImageProcessingError):
    """不支持的格式异常"""
    pass

class ProcessingTimeoutError(ImageProcessingError):
    """处理超时异常"""
    pass
```

#### 修改 `app/routers/v1.py` 的异常处理

**原代码**：
```python
except Exception as e:
    raise HTTPException(status_code=400, detail=str(e))
```

**修复代码**：
```python
import logging
from app.exceptions import *
from app.config import settings

logger = logging.getLogger(__name__)

try:
    # 现有处理逻辑
    result = run_pipeline(...)
    
except InvalidImageError as e:
    logger.warning(f"Invalid image provided: {e}")
    raise HTTPException(status_code=400, detail="Invalid image format or corrupted")
    
except ImageTooLargeError as e:
    logger.warning(f"Image too large: {e}")
    raise HTTPException(status_code=413, detail="Image size exceeds limit")
    
except UnsupportedFormatError as e:
    logger.warning(f"Unsupported format: {e}")
    raise HTTPException(status_code=400, detail="Unsupported image format")
    
except ProcessingTimeoutError as e:
    logger.error(f"Processing timeout: {e}")
    raise HTTPException(status_code=504, detail="Processing timeout")
    
except Exception as e:
    logger.error(f"Unexpected error in image processing: {e}")
    raise HTTPException(status_code=500, detail="Internal processing error")
```

### 5. 添加输入验证

#### 创建 `app/validation.py`
```python
from typing import Optional
from fastapi import UploadFile, HTTPException
from app.config import settings
from app.exceptions import *

def validate_uploaded_file(image: UploadFile) -> None:
    """验证上传的文件"""
    if not image:
        return
        
    # 检查文件大小
    if hasattr(image, 'size') and image.size > settings.max_image_size:
        raise ImageTooLargeError(f"Image size {image.size} exceeds limit {settings.max_image_size}")
    
    # 检查文件类型
    if image.content_type:
        if not image.content_type.startswith('image/'):
            raise UnsupportedFormatError(f"Content type {image.content_type} not supported")
        
        format_name = image.content_type.split('/')[-1].lower()
        if format_name not in settings.supported_formats:
            raise UnsupportedFormatError(f"Format {format_name} not supported")

def validate_base64_image(image_base64: Optional[str]) -> None:
    """验证base64图像"""
    if not image_base64:
        return
        
    import base64
    
    try:
        # 尝试解码
        if ',' in image_base64:
            image_base64 = image_base64.split(',')[1]
        
        decoded = base64.b64decode(image_base64)
        
        # 检查大小
        if len(decoded) > settings.max_image_size:
            raise ImageTooLargeError(f"Decoded image size exceeds limit")
            
    except Exception as e:
        raise InvalidImageError(f"Invalid base64 image: {e}")

def validate_image_url(image_url: Optional[str]) -> None:
    """验证图像URL"""
    if not image_url:
        return
        
    import re
    
    # 基础URL格式检查
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    if not url_pattern.match(image_url):
        raise InvalidImageError("Invalid URL format")
```

#### 在 `app/routers/v1.py` 中使用验证
```python
from app.validation import validate_uploaded_file, validate_base64_image, validate_image_url

@router.post('/rectify', response_model=RectifyResult, tags=["rectify"])
async def rectify(
    payload: Optional[RectifyJSONRequest] = None,
    image: Optional[UploadFile] = File(default=None),
    idempotency_key: Optional[str] = Header(default=None, alias="Idempotency-Key"),
):
    request_id = generate_request_id()
    
    try:
        # 输入验证
        validate_uploaded_file(image)
        if payload:
            validate_base64_image(payload.image_base64)
            validate_image_url(payload.image_url)
        
        # 检查是否提供了图像
        if not image and not (payload and (payload.image_base64 or payload.image_url)):
            raise InvalidImageError("No image provided")
        
        # 现有处理逻辑...
        
    except (InvalidImageError, ImageTooLargeError, UnsupportedFormatError) as e:
        # 已在上面的异常处理中处理
        raise
```

### 6. 添加结构化日志系统

#### 创建 `app/logging.py`
```python
import json
import logging
import uuid
from datetime import datetime
from typing import Optional
from contextvars import ContextVar

# 全局的request_id上下文变量
request_id_var: ContextVar[Optional[str]] = ContextVar('request_id', default=None)

class JSONFormatter(logging.Formatter):
    """JSON格式的日志格式化器"""
    
    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "request_id": request_id_var.get(),
        }
        
        # 添加额外的字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
            
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
            
        return json.dumps(log_entry, ensure_ascii=False)

def setup_logging(log_level: str = "INFO", log_format: str = "json"):
    """设置日志系统"""
    level = getattr(logging, log_level.upper())
    
    # 清除现有的handlers
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    handler = logging.StreamHandler()
    
    if log_format.lower() == "json":
        handler.setFormatter(JSONFormatter())
    else:
        handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
    
    root_logger.addHandler(handler)
    root_logger.setLevel(level)
    
    return root_logger

def generate_request_id() -> str:
    """生成请求ID"""
    return f"req_{uuid.uuid4().hex[:8]}"

def set_request_id(request_id: str):
    """设置当前请求的ID"""
    request_id_var.set(request_id)

def get_request_id() -> Optional[str]:
    """获取当前请求的ID"""
    return request_id_var.get()

def log_with_extra(logger, level: str, message: str, **extra_fields):
    """记录带有额外字段的日志"""
    record = logging.LogRecord(
        name=logger.name,
        level=getattr(logging, level.upper()),
        pathname="",
        lineno=0,
        msg=message,
        args=(),
        exc_info=None
    )
    record.extra_fields = extra_fields
    logger.handle(record)
```

#### 在 `app/routers/v1.py` 中使用日志

**在文件顶部添加**：
```python
import logging
from app.logging import generate_request_id, set_request_id, log_with_extra

logger = logging.getLogger(__name__)
```

**修改rectify函数，添加request_id和日志**：
```python
@router.post('/rectify', response_model=RectifyResult, tags=["rectify"])
async def rectify(
    payload: Optional[RectifyJSONRequest] = None,
    image: Optional[UploadFile] = File(default=None),
    idempotency_key: Optional[str] = Header(default=None, alias="Idempotency-Key"),
):
    # 生成并设置request_id
    request_id = generate_request_id()
    set_request_id(request_id)
    
    logger.info("Processing rectify request", extra={
        "request_id": request_id,
        "has_image": image is not None,
        "has_payload": payload is not None,
        "idempotency_key": idempotency_key
    })
    
    try:
        # 输入验证
        validate_uploaded_file(image)
        if payload:
            validate_base64_image(payload.image_base64)
            validate_image_url(payload.image_url)
        
        # 检查是否提供了图像
        if not image and not (payload and (payload.image_base64 or payload.image_url)):
            raise InvalidImageError("No image provided")
        
        # 记录处理开始
        logger.info("Starting image processing")
        
        # 现有处理逻辑...
        image_bytes = None
        image_b64 = None
        image_url = None
        options = PipelineOptions()
        
        # ... 现有逻辑保持不变 ...
        
        result = run_pipeline(image_bytes=image_bytes, image_base64=image_b64, image_url=image_url, options=options)
        
        # 记录处理成功
        logger.info("Image processing completed successfully", extra={
            "processing_time_ms": result.timings.get('total_ms'),
            "image_size": result.size,
            "detection_score": result.score
        })
        
        return RectifyResult(
            id=f"img_{uuid.uuid4().hex[:8]}",
            points=[(float(x), float(y)) for (x, y) in result.points],
            score=result.score,
            angle=result.angle,
            size=Size(w=result.size[0], h=result.size[1]),
            rectified_image=result.rectified_b64,
            timings=Timings(
                detect_ms=result.timings.get('detect_ms'),
                enhance_ms=result.timings.get('post_ms'),
                total_ms=result.timings.get('total_ms'),
            ),
            request_id=request_id,
        )
        
    except (InvalidImageError, ImageTooLargeError, UnsupportedFormatError) as e:
        logger.warning("Client error in image processing", extra={"error": str(e)})
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error in image processing", extra={"error": str(e)})
        raise HTTPException(status_code=500, detail="Internal processing error")
```

### 7. 添加冒烟测试

#### 创建测试目录和基础文件
```bash
mkdir -p tests
touch tests/__init__.py
```

#### 创建 `tests/conftest.py`
```python
import pytest
from fastapi.testclient import TestClient
from app.main import app

@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app)

@pytest.fixture
def sample_image_bytes():
    """创建一个简单的测试图像（1x1像素的PNG）"""
    import base64
    # 1x1像素的白色PNG图像的base64编码
    png_1x1 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    return base64.b64decode(png_1x1)

@pytest.fixture
def sample_image_base64():
    """返回测试图像的base64编码"""
    return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
```

#### 创建 `tests/test_smoke.py`
```python
import pytest
from fastapi.testclient import TestClient
import io

def test_health_endpoint(client):
    """测试健康检查端点"""
    response = client.get("/v1/health")
    assert response.status_code == 200
    
    data = response.json()
    assert "status" in data
    assert data["status"] in ["ok", "degraded", "error"]
    assert "version" in data

def test_rectify_endpoint_with_file(client, sample_image_bytes):
    """测试文件上传的rectify端点"""
    files = {"image": ("test.png", io.BytesIO(sample_image_bytes), "image/png")}
    
    response = client.post("/v1/rectify", files=files)
    
    # 应该成功或者有明确的错误信息
    assert response.status_code in [200, 400, 500]
    
    if response.status_code == 200:
        data = response.json()
        assert "id" in data
        assert "request_id" in data
        assert data["id"] is not None
        assert data["request_id"] is not None
        # 确保不是硬编码的值
        assert data["id"] != "img_mvp"
        assert data["request_id"] != "req_mvp"
    else:
        # 即使失败，也应该有合理的错误信息
        data = response.json()
        assert "detail" in data or "message" in data

def test_rectify_endpoint_with_base64(client, sample_image_base64):
    """测试base64输入的rectify端点"""
    payload = {
        "image_base64": f"data:image/png;base64,{sample_image_base64}"
    }
    
    response = client.post("/v1/rectify", json=payload)
    
    # 应该成功或者有明确的错误信息
    assert response.status_code in [200, 400, 500]
    
    if response.status_code == 200:
        data = response.json()
        assert "id" in data
        assert "request_id" in data
        # 确保不是硬编码的值
        assert data["id"] != "img_mvp"
        assert data["request_id"] != "req_mvp"

def test_rectify_endpoint_no_image(client):
    """测试没有提供图像的情况"""
    response = client.post("/v1/rectify", json={})
    
    # 应该返回400错误
    assert response.status_code == 400
    data = response.json()
    assert "detail" in data

def test_jobs_endpoint_basic(client):
    """测试异步作业端点的基本功能"""
    payload = {
        "items": [
            {"id": "test1", "image_base64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="}
        ]
    }
    
    response = client.post("/v1/jobs", json=payload)
    assert response.status_code in [202, 501]  # 202成功或501未实现
    
    if response.status_code == 202:
        data = response.json()
        assert "job_id" in data
        assert data["job_id"] != "job_demo"  # 确保不是硬编码

def test_api_consistency(client):
    """测试API的一致性"""
    # 检查所有端点都能正常响应
    endpoints = [
        ("/v1/health", "GET"),
    ]
    
    for endpoint, method in endpoints:
        if method == "GET":
            response = client.get(endpoint)
        else:
            response = client.post(endpoint)
        
        # 至少不应该返回404
        assert response.status_code != 404
```

#### 创建 `tests/run_smoke.py`
```python
#!/usr/bin/env python3
"""
快速冒烟测试脚本
可以独立运行，不依赖pytest
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from fastapi.testclient import TestClient
from app.main import app

def run_smoke_tests():
    """运行基础的冒烟测试"""
    client = TestClient(app)
    
    print("🔍 开始冒烟测试...")
    
    # 测试1: 健康检查
    try:
        response = client.get("/v1/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        print("✅ 健康检查测试通过")
    except Exception as e:
        print(f"❌ 健康检查测试失败: {e}")
        return False
    
    # 测试2: API基本结构
    try:
        # 测试没有图像的情况
        response = client.post("/v1/rectify", json={})
        # 应该返回400而不是500
        assert response.status_code == 400
        print("✅ API错误处理测试通过")
    except Exception as e:
        print(f"❌ API错误处理测试失败: {e}")
        return False
    
    # 测试3: 检查硬编码问题
    try:
        # 创建一个虚拟的小图像
        small_image = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\rIDATx\xdac\xf8\xff\x9f\x81\x1e\x00\x07\x82\x02\x7f<\xc8H\xef\x00\x00\x00\x00IEND\xaeB`\x82'
        
        files = {"image": ("test.png", small_image, "image/png")}
        response = client.post("/v1/rectify", files=files)
        
        if response.status_code == 200:
            data = response.json()
            # 检查是否还有硬编码值
            if data.get("id") == "img_mvp" or data.get("request_id") == "req_mvp":
                print("❌ 发现硬编码值，需要修复")
                return False
            print("✅ 硬编码检查通过")
        else:
            print("⚠️  图像处理返回错误（可能正常，取决于图像处理逻辑）")
    except Exception as e:
        print(f"⚠️  硬编码检查遇到问题: {e}")
    
    print("🎉 冒烟测试完成")
    return True

if __name__ == "__main__":
    success = run_smoke_tests()
    sys.exit(0 if success else 1)
```

### 8. 修改应用启动配置

#### 修改 `app/main.py`
```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from starlette.responses import JSONResponse
import logging

from app.routers.v1 import router as api_v1
from app.config import settings
from app.logging import setup_logging

# 设置日志系统
setup_logging(settings.log_level, settings.log_format)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.api_title,
    version=settings.api_version
)

# 更安全的CORS设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 替换 ["*"]
    allow_credentials=True,
    allow_methods=["GET", "POST"],  # 替换 ["*"]
    allow_headers=["Authorization", "Content-Type", "Idempotency-Key"],  # 替换 ["*"]
)

@app.get("/v1/health", tags=["system"], include_in_schema=False)
async def health():
    # 真实的健康检查
    try:
        # 检查配置
        config_ok = bool(settings.api_title)
        
        # 检查依赖模块
        import cv2
        import numpy as np
        modules_ok = True
        
        return {
            "status": "ok" if (config_ok and modules_ok) else "degraded",
            "version": settings.api_version,
            "config_loaded": config_ok,
            "modules_loaded": modules_ok
        }
    except Exception as e:
        return {"status": "error", "error": str(e)}

app.include_router(api_v1, prefix="/v1")

@app.exception_handler(Exception)
async def unhandled_exception_handler(request, exc):
    logging.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500, 
        content={
            "code": "INTERNAL_ERROR", 
            "message": "An unexpected error occurred",
            "request_id": getattr(request.state, 'request_id', None)
        }
    )
```

### 7. 创建启动脚本

#### 创建 `start.py`
```python
#!/usr/bin/env python3
import uvicorn
from app.config import settings

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=True,
        log_level=settings.log_level.lower()
    )
```

### 9. 创建 Docker 支持

#### 创建 `Dockerfile`
```dockerfile
FROM python:3.10-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgl1-mesa-glx \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "start.py"]
```

#### 创建 `.dockerignore`
```
__pycache__
*.pyc
*.pyo
*.pyd
.git
.gitignore
README.md
.env
.venv
tests/
docs/
```

### 10. 更新文档

#### 创建 `README.md`
```markdown
# 图像文档矫正API

## 快速开始

### 本地运行
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 复制配置文件
cp .env.example .env

# 3. 启动服务
python start.py
```

### Docker运行
```bash
# 构建镜像
docker build -t image-rectification .

# 运行容器
docker run -p 8000:8000 image-rectification
```

### API测试
```bash
# 健康检查
curl http://localhost:8000/v1/health

# 上传文件测试
curl -X POST "http://localhost:8000/v1/rectify" \
  -H "Content-Type: multipart/form-data" \
  -F "image=@test.jpg"
```

## 配置说明

所有配置项在 `.env` 文件中设置，参考 `.env.example`。
```

---

## 🚀 执行步骤

### 第一步：创建基础文件（15分钟）
```bash
# 在项目根目录执行
cd /path/to/image-rectification

# 1. 创建requirements.txt（包含测试依赖）
# 2. 创建.env.example  
# 3. 创建config.py
# 4. 创建app/logging.py
# 5. 创建app/exceptions.py
# 6. 创建app/validation.py
# 按上面的内容逐个创建
```

### 第二步：添加日志和测试（25分钟）
1. 创建 `tests/` 目录和测试文件
2. 创建 `tests/conftest.py`、`tests/test_smoke.py`、`tests/run_smoke.py`
3. 修改 `app/routers/v1.py` - 添加日志和request_id
4. 修改 `app/main.py` - 集成日志系统

### 第三步：修改现有代码（30分钟）
1. 修改 `app/routers/v1.py` - 去除硬编码，添加验证
2. 更新异常处理逻辑
3. 集成日志记录

### 第四步：测试修复结果（20分钟）
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 创建配置文件
cp .env.example .env

# 3. 启动服务
python start.py

# 4. 运行冒烟测试
python tests/run_smoke.py

# 5. 测试API
curl http://localhost:8000/v1/health
```

### 第五步：Docker化（20分钟）
1. 创建 Dockerfile
2. 创建 .dockerignore
3. 构建和测试镜像

---

## ✅ 验收清单

修复完成后，确保以下项目都能正常工作：

### 基础设施
- [ ] `pip install -r requirements.txt` 成功安装依赖（包含测试依赖）
- [ ] 服务可以正常启动，没有硬编码值
- [ ] 可以通过环境变量配置参数
- [ ] Docker镜像可以成功构建和运行

### 功能验证
- [ ] `/v1/health` 返回真实的健康状态
- [ ] API调用有合理的错误处理和结构化日志
- [ ] 每个请求都有唯一的request_id
- [ ] 冒烟测试可以成功运行 `python tests/run_smoke.py`

### 日志和调试
- [ ] 所有请求都有JSON格式的结构化日志
- [ ] 可以通过request_id追踪完整请求链路
- [ ] 错误日志包含足够的上下文信息
- [ ] 日志级别可以通过配置调整

### 测试保障
- [ ] 冒烟测试覆盖核心API端点
- [ ] 测试可以检测硬编码值问题
- [ ] 测试可以验证错误处理逻辑
- [ ] 可以通过pytest运行完整测试套件

### 文档和部署
- [ ] 有基础的部署文档和API使用示例
- [ ] 配置文件有完整的注释说明
- [ ] Docker部署一次成功

完成这些修复后，系统就从"技术验证demo"变成了"真正可运行、可调试、可测试的MVP基础"。