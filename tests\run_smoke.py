#!/usr/bin/env python3
"""
快速冒烟测试脚本
可以独立运行，不依赖pytest
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from fastapi.testclient import TestClient
from app.main import app

def run_smoke_tests():
    """运行基础的冒烟测试"""
    client = TestClient(app)
    
    print("🔍 开始冒烟测试...")
    
    # 测试1: 健康检查
    try:
        response = client.get("/v1/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        print("✅ 健康检查测试通过")
    except Exception as e:
        print(f"❌ 健康检查测试失败: {e}")
        return False
    
    # 测试2: API基本结构
    try:
        # 测试没有图像的情况
        response = client.post("/v1/rectify", json={})
        # 应该返回400而不是500
        assert response.status_code == 400
        print("✅ API错误处理测试通过")
    except Exception as e:
        print(f"❌ API错误处理测试失败: {e}")
        return False
    
    # 测试3: 检查硬编码问题
    try:
        # 使用真实的测试图像
        import os
        image_path = os.path.join(os.path.dirname(__file__), "..", "data", "待矫正图_01.jpg")
        with open(image_path, "rb") as f:
            real_image = f.read()

        files = {"image": ("test.jpg", real_image, "image/jpeg")}
        response = client.post("/v1/rectify", files=files)
        
        if response.status_code == 200:
            data = response.json()
            # 检查是否还有硬编码值
            if data.get("id") == "img_mvp" or data.get("request_id") == "req_mvp":
                print("❌ 发现硬编码值，需要修复")
                return False
            print("✅ 硬编码检查通过")
        else:
            print("⚠️  图像处理返回错误（可能正常，取决于图像处理逻辑）")
    except Exception as e:
        print(f"⚠️  硬编码检查遇到问题: {e}")
    
    print("🎉 冒烟测试完成")
    return True

if __name__ == "__main__":
    success = run_smoke_tests()
    sys.exit(0 if success else 1)
