import os
from typing import List
from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    # 服务配置
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_title: str = "Image Rectification API"
    api_version: str = "1.0.0"

    # 图像处理配置
    max_image_size: int = 10 * 1024 * 1024  # 10MB
    supported_formats: str = "jpeg,jpg,png"  # 改为字符串，稍后解析
    processing_timeout: int = 30

    # 日志配置
    log_level: str = "INFO"
    log_format: str = "json"

    # CORS配置
    allowed_origins: str = "http://localhost:3000,http://localhost:8080"

    # 算法参数
    canny_low_threshold: int = 50
    canny_high_threshold: int = 150
    clahe_clip_limit: float = 2.0

    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }

    @property
    def supported_formats_list(self) -> List[str]:
        """将逗号分隔的格式字符串转换为列表"""
        return [fmt.strip() for fmt in self.supported_formats.split(",")]

    @property
    def allowed_origins_list(self) -> List[str]:
        """将逗号分隔的CORS来源字符串转换为列表"""
        return [origin.strip() for origin in self.allowed_origins.split(",")]

# 全局配置实例
settings = Settings()
