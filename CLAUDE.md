# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Running the application
```bash
# 本地开发
python start.py

# Docker
docker build -t image-rectification .
docker run -p 8000:8000 image-rectification
```

### Testing
```bash
# 快速冒烟测试（推荐首先运行）
python tests/run_smoke.py

# 完整测试套件
pytest tests/

# 运行特定测试文件
pytest tests/test_api_endpoints.py -v
pytest tests/test_performance.py -v

# 压力测试（运行较慢）
pytest tests/test_performance.py::TestScalability::test_stress_test -v -s
```

### Configuration
```bash
# 创建配置文件
cp .env.example .env
# 然后编辑 .env 文件设置具体参数
```

## 架构概述

### 核心组件
- **FastAPI应用** (`app/main.py`): ASGI入口，配置CORS和全局异常处理
- **API路由** (`app/routers/v1.py`): REST API端点，主要是 `/v1/rectify` 同步处理
- **图像处理流水线** (`app/pipeline/traditional.py`): 文档检测、透视矫正、图像增强的核心算法
- **配置管理** (`app/config.py`): 基于pydantic-settings的环境变量配置
- **结构化日志** (`app/logging.py`): 支持JSON格式和request_id追踪

### 数据流
1. 客户端上传图像 (multipart/form-data 或 JSON base64)
2. 输入验证 (`app/validation.py`) 检查格式、大小等
3. 图像处理流水线执行：文档检测 → 透视变换 → 自动纠偏 → 图像增强
4. 返回处理结果的base64编码和元数据

### 配置热点
- 图像大小限制: `MAX_IMAGE_SIZE` (默认10MB)
- 支持格式: `SUPPORTED_FORMATS` (jpeg,jpg,png)
- 算法参数: `CANNY_LOW_THRESHOLD`, `CANNY_HIGH_THRESHOLD`, `CLAHE_CLIP_LIMIT`
- 自动纠偏最大角度限制在 `PipelineOptions.deskew_max_angle` (12度)

## 测试数据
测试使用 `data/rotated_01.jpg`, `data/rotated_02.png` 等真实图像文件。注意：
- `conftest.py` 和 `run_smoke.py` 中可能仍引用旧的中文文件名，需要更新为新的文件名
- 测试图像应包含需要矫正的文档内容

## 已知注意事项
- 异步作业API (`/v1/jobs`) 目前只是占位实现
- API认证机制尚未实现  
- 性能监控指标需要进一步完善
- 错误处理已基本完善，但建议在高负载下进一步测试