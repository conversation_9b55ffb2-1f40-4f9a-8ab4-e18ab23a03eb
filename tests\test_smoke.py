import pytest
from fastapi.testclient import TestClient
import io

def test_health_endpoint(client):
    """测试健康检查端点"""
    response = client.get("/v1/health")
    assert response.status_code == 200
    
    data = response.json()
    assert "status" in data
    assert data["status"] in ["ok", "degraded", "error"]
    assert "version" in data

def test_rectify_endpoint_with_file(client, sample_image_bytes):
    """测试文件上传的rectify端点"""
    files = {"image": ("test.png", io.BytesIO(sample_image_bytes), "image/png")}
    
    response = client.post("/v1/rectify", files=files)
    
    # 应该成功或者有明确的错误信息
    assert response.status_code in [200, 400, 500]
    
    if response.status_code == 200:
        data = response.json()
        assert "id" in data
        assert "request_id" in data
        assert data["id"] is not None
        assert data["request_id"] is not None
        # 确保不是硬编码的值
        assert data["id"] != "img_mvp"
        assert data["request_id"] != "req_mvp"
    else:
        # 即使失败，也应该有合理的错误信息
        data = response.json()
        assert "detail" in data or "message" in data

def test_rectify_endpoint_with_base64(client, sample_image_base64):
    """测试base64输入的rectify端点"""
    payload = {
        "image_base64": f"data:image/png;base64,{sample_image_base64}"
    }
    
    response = client.post("/v1/rectify", json=payload)
    
    # 应该成功或者有明确的错误信息
    assert response.status_code in [200, 400, 500]
    
    if response.status_code == 200:
        data = response.json()
        assert "id" in data
        assert "request_id" in data
        # 确保不是硬编码的值
        assert data["id"] != "img_mvp"
        assert data["request_id"] != "req_mvp"

def test_rectify_endpoint_no_image(client):
    """测试没有提供图像的情况"""
    response = client.post("/v1/rectify", json={})
    
    # 应该返回400错误
    assert response.status_code == 400
    data = response.json()
    assert "detail" in data

def test_jobs_endpoint_basic(client):
    """测试异步作业端点的基本功能"""
    payload = {
        "items": [
            {"id": "test1", "image_base64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="}
        ]
    }
    
    response = client.post("/v1/jobs", json=payload)
    assert response.status_code in [202, 501]  # 202成功或501未实现
    
    if response.status_code == 202:
        data = response.json()
        assert "job_id" in data
        assert data["job_id"] != "job_demo"  # 确保不是硬编码

def test_api_consistency(client):
    """测试API的一致性"""
    # 检查所有端点都能正常响应
    endpoints = [
        ("/v1/health", "GET"),
    ]
    
    for endpoint, method in endpoints:
        if method == "GET":
            response = client.get(endpoint)
        else:
            response = client.post(endpoint)
        
        # 至少不应该返回404
        assert response.status_code != 404
