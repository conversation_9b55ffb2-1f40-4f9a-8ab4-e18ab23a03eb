#!/usr/bin/env python3
"""
使用requests直接测试API的去倾斜功能
"""

import requests
import json
from pathlib import Path

def test_api_deskew():
    """测试API的去倾斜功能"""
    print("🔍 测试API去倾斜功能...")
    
    # 检查data目录
    data_dir = Path("data")
    image_files = [f for f in data_dir.iterdir() 
                   if f.is_file() and f.suffix.lower() in {'.jpg', '.jpeg', '.png'}]
    
    if not image_files:
        print("❌ 没有找到图片文件")
        return
    
    # 测试第一张图片
    image_file = image_files[0]
    print(f"📸 测试图片: {image_file.name}")
    
    # 准备请求
    url = "http://localhost:8000/v1/rectify"
    
    # 选项参数
    options_data = {
        "enhance": {
            "brighten": True,
            "denoise": True,
            "sharpen": True,
            "binarize": False,
            "grayscale": False
        },
        "auto_deskew": True,
        "deskew_max_angle": 25.0,
        "return_points": True
    }
    
    # 文件和数据
    with open(image_file, 'rb') as f:
        files = {'image': (image_file.name, f, 'image/jpeg')}
        data = {'options': json.dumps(options_data)}
        
        print(f"🚀 发送请求...")
        print(f"   选项: {json.dumps(options_data, indent=2)}")
        
        response = requests.post(url, files=files, data=data, timeout=30)
    
    print(f"📊 响应状态: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 成功!")
        print(f"   🆔 图片ID: {result.get('id', 'N/A')}")
        print(f"   📍 检测分数: {result.get('score', 'N/A')}")
        print(f"   🔄 旋转角度: {result.get('angle', 'N/A')}°")
        print(f"   📐 图片尺寸: {result.get('size', {}).get('w', 'N/A')}x{result.get('size', {}).get('h', 'N/A')}")
        
        if 'timings' in result and result['timings']:
            timings = result['timings']
            print(f"   ⏱️  处理时间: {timings.get('total_ms', 'N/A')}ms")
        
        # 保存结果
        if 'rectified_image' in result and result['rectified_image']:
            import base64
            output_file = Path("output_curl_test.jpg")
            image_data = base64.b64decode(result['rectified_image'])
            with open(output_file, 'wb') as f:
                f.write(image_data)
            print(f"   💾 已保存: {output_file}")
        
        return result.get('angle', 0.0)
    else:
        print(f"❌ 请求失败: {response.text}")
        return 0.0

if __name__ == "__main__":
    angle = test_api_deskew()
    print(f"\n🎯 最终检测角度: {angle}°")
    if abs(angle) > 0.1:
        print("✅ 成功检测到倾斜角度!")
    else:
        print("❌ 未检测到明显倾斜角度")
