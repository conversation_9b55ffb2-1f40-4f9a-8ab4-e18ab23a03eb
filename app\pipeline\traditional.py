import base64
import io
import time
from dataclasses import dataclass
from typing import List, Optional, <PERSON>ple

import cv2
import numpy as np
import requests

Point = Tuple[float, float]

@dataclass
class PipelineOptions:
    brighten: bool = True
    grayscale: bool = False
    binarize: bool = False
    denoise: bool = False
    sharpen: bool = False
    auto_deskew: bool = True
    deskew_max_angle: float = 25.0  # 最大纠偏角度（度）

@dataclass
class PipelineResult:
    points: Optional[List[Point]]
    angle: float
    score: float
    size: Tuple[int, int]
    rectified_b64: Optional[str]
    timings: dict


def load_image(image_bytes: Optional[bytes] = None, image_base64: Optional[str] = None, image_url: Optional[str] = None) -> np.ndarray:
    if image_bytes is not None:
        data = np.frombuffer(image_bytes, np.uint8)
        img = cv2.imdecode(data, cv2.IMREAD_COLOR)
        return img
    if image_base64:
        try:
            raw = base64.b64decode(image_base64)
        except Exception:
            # try strip header
            head_sep = image_base64.find(',')
            raw = base64.b64decode(image_base64[head_sep+1:] if head_sep>0 else image_base64)
        data = np.frombuffer(raw, np.uint8)
        img = cv2.imdecode(data, cv2.IMREAD_COLOR)
        return img
    if image_url:
        resp = requests.get(image_url, timeout=10)
        resp.raise_for_status()
        data = np.frombuffer(resp.content, np.uint8)
        img = cv2.imdecode(data, cv2.IMREAD_COLOR)
        return img
    raise ValueError("No image provided")


def order_points(pts: np.ndarray) -> np.ndarray:
    # pts: (4,2)
    rect = np.zeros((4, 2), dtype="float32")
    s = pts.sum(axis=1)
    rect[0] = pts[np.argmin(s)]  # top-left
    rect[2] = pts[np.argmax(s)]  # bottom-right

    diff = np.diff(pts, axis=1)
    rect[1] = pts[np.argmin(diff)]  # top-right
    rect[3] = pts[np.argmax(diff)]  # bottom-left
    return rect


def four_point_transform(image: np.ndarray, pts: np.ndarray) -> np.ndarray:
    rect = order_points(pts)
    (tl, tr, br, bl) = rect

    widthA = np.linalg.norm(br - bl)
    widthB = np.linalg.norm(tr - tl)
    maxWidth = int(max(widthA, widthB))

    heightA = np.linalg.norm(tr - br)
    heightB = np.linalg.norm(tl - bl)
    maxHeight = int(max(heightA, heightB))

    dst = np.array([
        [0, 0],
        [maxWidth - 1, 0],
        [maxWidth - 1, maxHeight - 1],
        [0, maxHeight - 1]
    ], dtype="float32")

    M = cv2.getPerspectiveTransform(rect, dst)
    warped = cv2.warpPerspective(image, M, (maxWidth, maxHeight))
    return warped


def detect_document_contour(image: np.ndarray) -> Tuple[Optional[np.ndarray], float]:
    # returns (4x2 points, score)
    img = image.copy()
    h, w = img.shape[:2]
    ratio = 800.0 / max(h, w)
    if ratio < 1:
        img_small = cv2.resize(img, (int(w*ratio), int(h*ratio)))
    else:
        img_small = img
    gray = cv2.cvtColor(img_small, cv2.COLOR_BGR2GRAY)
    gray = cv2.GaussianBlur(gray, (5, 5), 0)

    edges = cv2.Canny(gray, 50, 150)
    edges = cv2.dilate(edges, None, iterations=1)

    contours, _ = cv2.findContours(edges, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    contours = sorted(contours, key=cv2.contourArea, reverse=True)[:10]

    best_quad = None
    best_score = 0.0
    for cnt in contours:
        peri = cv2.arcLength(cnt, True)
        approx = cv2.approxPolyDP(cnt, 0.02 * peri, True)
        if len(approx) == 4 and cv2.isContourConvex(approx):
            area = cv2.contourArea(approx)
            if area < (img_small.shape[0]*img_small.shape[1])*0.1:
                continue
            # score: area ratio and rectangularity
            rect = cv2.minAreaRect(approx)
            box = cv2.boxPoints(rect)
            box_area = cv2.contourArea(box.astype(np.int32))
            rectangularity = float(area) / (box_area + 1e-6)
            score = rectangularity * (area / (img_small.shape[0]*img_small.shape[1]))
            if score > best_score:
                best_score = score
                best_quad = approx.reshape(4, 2)

    if best_quad is not None:
        # scale back to original size
        scale = 1.0/ratio if ratio < 1 else 1.0
        quad = best_quad.astype(np.float32) * scale
        return quad, float(best_score)
    return None, 0.0


def enhance_image(img: np.ndarray, opts: PipelineOptions) -> np.ndarray:
    out = img.copy()
    if opts.brighten:
        lab = cv2.cvtColor(out, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        cl = clahe.apply(l)
        lab = cv2.merge((cl, a, b))
        out = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
    if opts.denoise:
        out = cv2.fastNlMeansDenoisingColored(out, None, 3, 3, 7, 21)
    if opts.sharpen:
        kernel = np.array([[0, -1, 0], [-1, 5, -1], [0, -1, 0]])
        out = cv2.filter2D(out, -1, kernel)
    if opts.grayscale:
        out = cv2.cvtColor(out, cv2.COLOR_BGR2GRAY)
        out = cv2.cvtColor(out, cv2.COLOR_GRAY2BGR)
    if opts.binarize:
        gray = cv2.cvtColor(out, cv2.COLOR_BGR2GRAY)
        th = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                   cv2.THRESH_BINARY, 35, 10)
        out = cv2.cvtColor(th, cv2.COLOR_GRAY2BGR)
    return out


def compute_skew_angle(image_bgr: np.ndarray, max_angle: float = 25.0) -> float:
    """估计图像的倾斜角度（度）。优先使用霍夫直线角度的中位数，回退到PCA主方向。
    返回角度范围约为 [-max_angle, max_angle]。
    """
    h, w = image_bgr.shape[:2]
    if h < 10 or w < 10:
        return 0.0

    gray = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2GRAY)
    gray = cv2.GaussianBlur(gray, (3, 3), 0)
    _, bw = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # 将文本视为白色前景，反相更稳健
    foreground = cv2.bitwise_not(bw)

    # 合并同一行文本区域（横向形态学闭运算）- 更激进的连接
    kx = max(20, w // 50)  # 增大核宽度
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (kx, 3))
    merged = cv2.morphologyEx(foreground, cv2.MORPH_CLOSE, kernel, iterations=2)  # 增加迭代

    # 边缘和直线 - 更宽松的参数
    edges = cv2.Canny(merged, 30, 120)  # 降低边缘检测阈值
    lines = cv2.HoughLinesP(
        edges, 1, np.pi / 180, threshold=50,  # 降低霍夫阈值
        minLineLength=max(20, w // 30),  # 降低最小线段长度
        maxLineGap=max(15, w // 60)      # 增大允许间隙
    )

    angles = []
    weights = []
    if lines is not None:
        for x1, y1, x2, y2 in lines[:, 0]:
            dx, dy = x2 - x1, y2 - y1
            if dx == 0 and dy == 0:
                continue
            length = float(np.hypot(dx, dy))
            if length < max(10, w // 100):  # 忽略过短的线段
                continue
            ang = np.degrees(np.arctan2(dy, dx))
            # 只考虑近水平线，统一到 [-45,45]
            if -60 <= ang <= 60:
                if ang > 45:
                    ang -= 90
                if ang < -45:
                    ang += 90
                angles.append(ang)
                weights.append(length)

    if len(angles) >= 3:  # 降低要求从5到3
        # 基于加权直方图寻找主峰
        angles_arr = np.array(angles, dtype=np.float32)
        weights_arr = np.array(weights, dtype=np.float32) if weights else np.ones_like(angles_arr)
        # 1度步长直方图
        bins = np.linspace(-45, 45, 91)
        hist, bin_edges = np.histogram(angles_arr, bins=bins, weights=weights_arr)
        # 找到主峰bin，若主峰太靠近0°，尝试选择次峰且角度>=5°
        peak_idx = int(np.argmax(hist))
        candidate_idxs = np.argsort(hist)[::-1]
        chosen_idx = peak_idx
        for idx in candidate_idxs:
            center = (bin_edges[idx] + bin_edges[idx+1]) * 0.5
            if abs(center) >= 5.0 or idx == candidate_idxs[0]:
                chosen_idx = idx
                break
        center = (bin_edges[chosen_idx] + bin_edges[chosen_idx+1]) * 0.5
        # 在所选bin附近 +/-1.5 度范围内做加权平均
        sel = (angles_arr >= center - 1.5) & (angles_arr <= center + 1.5)
        if not np.any(sel):
            angle_deg = float(center)
        else:
            angle_deg = float(np.average(angles_arr[sel], weights=weights_arr[sel]))
    else:
        # 回退：PCA 主方向
        ys, xs = np.where(merged > 0)
        if len(xs) < 50:
            return 0.0
        coords = np.column_stack((xs, ys)).astype(np.float32)
        mean, eigenvectors = cv2.PCACompute(coords, mean=np.array([]))
        v = eigenvectors[0]
        angle_deg = np.degrees(np.arctan2(v[1], v[0]))
        if angle_deg > 45:
            angle_deg -= 90
        if angle_deg < -45:
            angle_deg += 90

    angle_deg = float(np.clip(angle_deg, -max_angle, max_angle))
    # 小角度视为无需纠偏 - 降低阈值以捕捉轻微倾斜
    if abs(angle_deg) < 0.05:
        return 0.0
    return angle_deg


def encode_image_to_base64(img: np.ndarray, ext: str = '.jpg') -> str:
    params = [int(cv2.IMWRITE_JPEG_QUALITY), 90] if ext == '.jpg' else []
    ok, buf = cv2.imencode(ext, img, params)
    if not ok:
        raise RuntimeError('encode failed')
    return base64.b64encode(buf.tobytes()).decode('ascii')


def run_pipeline(image_bytes: Optional[bytes] = None, image_base64: Optional[str] = None, image_url: Optional[str] = None,
                 options: Optional[PipelineOptions] = None) -> PipelineResult:
    t0 = time.time()
    img = load_image(image_bytes=image_bytes, image_base64=image_base64, image_url=image_url)
    if img is None:
        raise ValueError('INVALID_IMAGE')
    h, w = img.shape[:2]

    t1 = time.time()
    quad, score = detect_document_contour(img)
    t2 = time.time()

    rectified = None
    angle = 0.0
    if quad is not None:
        rectified = four_point_transform(img, quad.astype(np.float32))
    else:
        # fallback: return original
        quad = np.array([[0,0],[w-1,0],[w-1,h-1],[0,h-1]], dtype=np.float32)
        rectified = img.copy()

    # orientation heuristic & deskew
    opts = options or PipelineOptions()
    if opts.auto_deskew:
        try:
            angle_deg = compute_skew_angle(rectified, max_angle=opts.deskew_max_angle)
            if abs(angle_deg) > 0.0:
                (h2, w2) = rectified.shape[:2]
                center = (w2 // 2, h2 // 2)
                M2 = cv2.getRotationMatrix2D(center, angle_deg, 1.0)
                rectified = cv2.warpAffine(
                    rectified, M2, (w2, h2), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE
                )
                angle = angle_deg
        except Exception:
            pass

    # enhance
    enhanced = enhance_image(rectified, opts)
    t3 = time.time()

    b64 = encode_image_to_base64(enhanced)

    timings = {
        'decode_ms': (t1 - t0) * 1000.0,
        'detect_ms': (t2 - t1) * 1000.0,
        'post_ms': (t3 - t2) * 1000.0,
        'total_ms': (t3 - t0) * 1000.0,
    }

    size = (w, h)
    points = [(float(x), float(y)) for x, y in quad.tolist()]

    return PipelineResult(points=points, angle=angle, score=score, size=size, rectified_b64=b64, timings=timings)

