## 项目名称
图像文档智能矫正与增强 API（Python 后端）

### 文档信息
- 版本：v1.0（草案）
- 文档负责人：待定
- 评审人：产品、算法、后端、QA、运维
- 最近更新：初始化

## 背景与目标
在教育、办公、打印等场景中，用户常以手机拍摄文档，受复杂背景、光照不均、透视倾斜、纸张弯曲/折皱等影响，图像可读性差。本项目旨在提供一套高并发、高可靠的图像文档识别、切边和形变矫正服务，并支持按需增强（增亮、黑白等），以 API 形式供 App/服务端调用。

目标：
- 自动从复杂背景中检测出文档区域并裁切
- 矫正文档的倾斜、弯曲、形变、折皱等问题
- 提供多种增强模式（亮度/对比、去噪、锐化、二值化等）
- 输出切边图、矫正图及关键几何信息
- 提供高并发、高可靠的 REST API，便于业务接入与定制

## 术语
- 文档检测：在整幅图中检测含文档的区域（可一页或多页）
- 透视矫正：基于四点或轮廓将文档拉正
- 形变矫正（去弯曲）：对弯曲/起皱纸张进行几何展开
- 增强：提升可读性（亮度、对比度、去噪、二值化等）

## 目标用户与场景
- 教育文件处理：作业/试卷拍照上传清晰化
- 办公文档处理：合同票据归档、扫描件优化
- 打印预处理：打印前纠偏、二值化、提高清晰度

## 成功指标（KPIs）
- 业务指标：
  - 任务成功率 ≥ 99.9%
  - 有效文档检测召回率 ≥ 98%，精度 ≥ 97%（基于标注集）
  - 主流程错误率（5xx）≤ 0.1%
- 性能指标：
  - 在线同步接口 p95 延迟 ≤ 800ms（1080p 单页，CPU 参考；GPU 可更低）
  - 异步批处理吞吐 ≥ 20 QPS/实例（按配置可横向扩展）
- 可靠性：
  - 服务可用性 ≥ 99.9%，SLO 达标

## 范围与不在范围
- 在范围：单页/多页文档检测、切边、透视矫正、弯曲/折皱去形变、图像增强、同步/异步 API、作业与队列、监控与限流
- 暂不包含：OCR 识别、表单结构化、印章鉴伪、手写识别（可作为后续扩展或由上游服务完成）

## 需求概览
- 输入：JPEG/PNG（base64/URL/直传文件）、可选参数（增强模式、输出项、异步/同步）
- 输出：
  - 裁切后的图、矫正后的图
  - 文档四点/轮廓、多段网格/变形场（可选）
  - 处理日志（检测分数、步骤用时）
- 管控：鉴权、配额、限流、幂等、重试、审计日志

## 功能性需求
1) 文档检测与裁切
- 在复杂背景中定位文档区域；支持单页优先，后续扩展多页
- 返回四点坐标、置信度；可导出裁切图

2) 倾斜与透视矫正
- 基于四点进行透视变换，校正旋转与投影畸变
- 自动判断文档朝向（0/90/180/270）并旋正

3) 弯曲/形变/折皱矫正
- 估计文档曲面或文本行弯曲轨迹，进行非线性几何展开
- 提供“速度优先”和“质量优先”两种策略（可配）

4) 图像增强
- 亮度/对比/白平衡自适应
- 去噪、锐化、阴影补偿
- 二值化（自适应阈值/可调参数）与灰度/黑白模式

5) 输出与可视化
- 可选输出：切边图、矫正图、增强图、mask、顶点/轮廓坐标、形变网格可视化
- 支持原图尺寸/缩放尺寸输出；支持返回中间产物（调试开关）

6) 批处理与异步
- 同步接口用于小图/实时场景
- 异步作业接口用于大图/批量，支持队列、回调/轮询

7) 配置与策略
- 全局/租户级默认参数（增强强度、输出项、并发限额）
- 质量/性能预设：fast/balanced/high_quality

## 非功能性需求
- 并发与性能：支持水平扩展；CPU/GPU 可配置；Uvicorn/Gunicorn 多 worker；异步队列
- 可靠性：健康检查、熔断/限流、重试、幂等、超时、降级；冪等 Idempotency-Key
- 安全：HTTPS、鉴权（API Key/JWT）、最小权限、请求体大小限制、内容审计钩子
- 合规与隐私：临时文件生命周期控制（默认 24h 内清理，可配置），可选不落盘
- 可观测性：结构化日志、trace、metrics（延迟、QPS、错误率、模型耗时）

## API 设计（v1 初稿）
- 鉴权：HTTP Header `Authorization: Bearer <token>` 或 `X-API-Key`
- Content-Type：multipart/form-data 或 application/json

1) 同步单张处理
- POST /v1/rectify
  - 入参（二选一）：
    - image: file（二进制）或 image_base64: string 或 image_url: string
  - 可选：
    - options: { mode: fast|balanced|hq, enhance: { brighten: bool, grayscale: bool, binarize: bool, denoise: bool, sharpen: bool }, outputs: { crop: bool, rectified: bool, mask: bool, debug: bool }, return_points: bool, return_mesh: bool }
  - 返回：
    - { id, points: [[x,y]x4], score, angle, size: {w,h}, crop_image(base64可选), rectified_image(base64可选), enhance: { applied: {...} }, mesh(optional), timings, request_id }

2) 异步批处理
- POST /v1/jobs
  - body: { items: [ { id, image|image_base64|image_url, options? } ], callback_url? }
  - 返回：{ job_id, queued }
- GET /v1/jobs/{job_id}
  - 返回：{ status: queued|processing|done|error, results: [...], errors: [...] }

3) 系统健康与配额
- GET /v1/health -> { status, uptime, version }
- GET /v1/quota -> { limit, used, reset_at }

4) 错误规范
- 4xx：参数/鉴权/配额；5xx：服务内部错误
- 统一错误体：{ code, message, request_id, details? }

## 技术方案（实现建议）
- 语言与框架：Python 3.10+，FastAPI + Uvicorn（ASGI），Pydantic v2
- 并发与扩展：
  - 同步路径：FastAPI（async）+ 线程池/进程池
  - 异步路径：Celery/RQ + Redis 作为队列；可选 GPU Worker 池
- 核心算法流水线：
  1. 预处理：缩放到最长边≤1280，色彩归一化
  2. 文档检测：边缘/轮廓 + 学习型检测（可插拔，ONNXRuntime/PyTorch）；返回候选四边形
  3. 透视矫正：单应变换（RANSAC 估计鲁棒）+ 朝向纠正
  4. 弯曲矫正：基于文本行/纹理流估计曲面，采用 TPS/网格形变进行展开（提供 fast/hq 两档）
  5. 增强：亮度/对比/阴影补偿、去噪、锐化、二值化
  6. 导出：按需编码为 JPEG/PNG/base64，生成几何/日志
- 模型管理：本地权重/版本号配置，冷启动加载，热更新预留
- 存储与缓存：
  - 临时对象存储（本地或 S3 兼容）+ 清理任务
  - 可选 CDN/URL 签名
- 部署：Docker 镜像；K8s/容器服务；水平扩展；就绪/存活探针

## 数据结构示例
- 请求（JSON 示例）：
{
  "image_base64": "...",
  "options": {
    "mode": "balanced",
    "enhance": {"brighten": true, "grayscale": false, "binarize": true},
    "outputs": {"crop": true, "rectified": true, "mask": false, "debug": false},
    "return_points": true
  }
}
- 响应（成功）：
{
  "id": "img_123",
  "points": [[12,34],[1024,28],[1009,1450],[18,1460]],
  "score": 0.992,
  "angle": 0,
  "size": {"w": 1920, "h": 1080},
  "rectified_image": "base64...",
  "timings": {"detect_ms": 60, "dewarp_ms": 120, "enhance_ms": 20, "total_ms": 220},
  "request_id": "req_xxx"
}
- 响应（错误）：
{ "code": "INVALID_IMAGE", "message": "image cannot be decoded", "request_id": "req_xxx" }

## 错误码定义（示例）
- INVALID_AUTH / RATE_LIMITED / QUOTA_EXCEEDED
- INVALID_IMAGE / UNSUPPORTED_FORMAT / IMAGE_TOO_LARGE
- DETECT_FAILED / RECTIFY_FAILED / DEWARP_FAILED
- TIMEOUT / INTERNAL_ERROR

## 监控与运维
- 指标：QPS、p50/p95/p99、错误率、超时、各阶段耗时、队列长度、GPU 利用率
- 日志：结构化 JSON，包含 request_id，采样记录中间结果（脱敏）
- Tracing：OpenTelemetry（HTTP/队列/模型调用链）
- 报警：错误率/延迟阈值、队列堆积、容器重启

## 安全与合规
- 全程 HTTPS；鉴权与配额控制；WAF/大小限制；病毒扫描（可选）
- 数据最小化：默认不持久化图片；可配置保留期与脱敏策略
- 审计：访问日志与管理员操作审计

## 测试与验收
- 单元测试：参数校验、几何变换、增强模块
- 集成测试：完整流水线（小样本集）
- 视觉指标：基于标注集评估检测 IoU、四点误差、去弯曲质量（参考文本行直度）
- 压测：并发 50/100/200 QPS，记录延迟曲线与错误率
- 验收准则：KPIs 达标、错误码/日志规范齐全、文档完善（OpenAPI/示例）

## 里程碑计划（建议）
- M1（1-2 周）：PRD/架构设计/样例数据/评测指标确定
- M2（2-3 周）：基础管线（检测+透视矫正+增强）与同步 API 完成
- M3（2-3 周）：弯曲矫正 v1、异步作业、监控与错误码完善
- M4（1-2 周）：压测与优化、容器化与发布、文档与Demo、试点接入

## 风险与缓解
- 弯曲矫正鲁棒性不足：提供 fast/hq 双档与手动回退（仅透视矫正）
- 复杂背景误检：引入置信阈值与多策略融合，提供可视化调参工具
- 性能瓶颈：模型量化/ONNXRuntime、批处理、并行化与缓存
- 法规与隐私：最小化存储与清理策略、访问控制

## 可定制化
- 可插拔检测/去弯曲模型与增强策略
- 画质与速度预设、每租户参数模板
- 回调与签名、私有化部署支持
