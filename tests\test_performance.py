import pytest
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

class TestPerformance:
    def test_single_request_performance(self, client, sample_image_bytes):
        """测试单个请求的性能"""
        files = {"image": ("test.jpg", sample_image_bytes, "image/jpeg")}
        
        start_time = time.time()
        response = client.post("/v1/rectify", files=files)
        end_time = time.time()
        
        if response.status_code == 200:
            processing_time = end_time - start_time
            print(f"Processing time: {processing_time:.3f}s")
            assert processing_time < 5.0  # 应该在5秒内完成
            
            # 检查返回的时间统计
            data = response.json()
            if "timings" in data and data["timings"]:
                total_ms = data["timings"].get("total_ms", 0)
                print(f"Server reported time: {total_ms:.1f}ms")
                assert total_ms > 0

    def test_concurrent_requests(self, client, sample_image_bytes):
        """测试并发请求处理能力"""
        def make_request():
            files = {"image": ("test.jpg", sample_image_bytes, "image/jpeg")}
            start_time = time.time()
            response = client.post("/v1/rectify", files=files)
            end_time = time.time()
            return response, end_time - start_time
        
        # 并发5个请求
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_request) for _ in range(5)]
            
            success_count = 0
            total_time = 0
            
            for future in as_completed(futures):
                response, request_time = future.result()
                if response.status_code == 200:
                    success_count += 1
                    total_time += request_time
            
            # 至少应该有一些成功的请求
            assert success_count > 0
            
            if success_count > 0:
                avg_time = total_time / success_count
                print(f"Concurrent requests: {success_count}/{len(futures)} succeeded")
                print(f"Average time: {avg_time:.3f}s")

    def test_health_endpoint_performance(self, client):
        """测试健康检查端点的性能"""
        times = []
        
        for _ in range(10):
            start_time = time.time()
            response = client.get("/v1/health")
            end_time = time.time()
            
            assert response.status_code == 200
            times.append(end_time - start_time)
        
        avg_time = sum(times) / len(times)
        max_time = max(times)
        
        print(f"Health check - Avg: {avg_time:.3f}s, Max: {max_time:.3f}s")
        assert avg_time < 0.1  # 健康检查应该很快
        assert max_time < 0.5

class TestMemoryUsage:
    def test_multiple_sequential_requests(self, client, sample_image_bytes):
        """测试多个连续请求是否有内存泄漏"""
        files = {"image": ("test.jpg", sample_image_bytes, "image/jpeg")}
        
        success_count = 0
        for i in range(10):
            response = client.post("/v1/rectify", files=files)
            if response.status_code == 200:
                success_count += 1
        
        print(f"Sequential requests: {success_count}/10 succeeded")
        assert success_count >= 8  # 允许少量失败

class TestScalability:
    @pytest.mark.slow
    def test_stress_test(self, client, sample_image_bytes):
        """压力测试 - 标记为slow，默认不运行"""
        files = {"image": ("test.jpg", sample_image_bytes, "image/jpeg")}
        
        def make_request():
            return client.post("/v1/rectify", files=files)
        
        # 更高并发度的压力测试
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(20)]
            
            success_count = 0
            error_count = 0
            
            for future in as_completed(futures):
                response = future.result()
                if response.status_code == 200:
                    success_count += 1
                else:
                    error_count += 1
            
            print(f"Stress test: {success_count} success, {error_count} errors")
            # 在压力测试中，允许一定的错误率
            success_rate = success_count / (success_count + error_count)
            assert success_rate >= 0.7  # 至少70%成功率
