# 图像文档智能矫正与增强 API

## 快速开始

### 本地运行
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 复制配置文件
cp .env.example .env

# 3. 启动服务
python start.py
```

### Docker运行
```bash
# 构建镜像
docker build -t image-rectification .

# 运行容器
docker run -p 8000:8000 image-rectification
```

### API测试
```bash
# 健康检查
curl http://localhost:8000/v1/health

# 上传文件测试
curl -X POST "http://localhost:8000/v1/rectify" \
  -H "Content-Type: multipart/form-data" \
  -F "image=@test.jpg"

# Base64测试
curl -X POST "http://localhost:8000/v1/rectify" \
  -H "Content-Type: application/json" \
  -d '{"image_base64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="}'
```

### 运行测试
```bash
# 快速冒烟测试
python tests/run_smoke.py

# 完整测试套件
pytest tests/

# 运行特定测试
pytest tests/test_api_endpoints.py -v

# 运行性能测试
pytest tests/test_performance.py -v

# 运行压力测试（较慢）
pytest tests/test_performance.py::TestScalability::test_stress_test -v -s
```

## 配置说明

所有配置项在 `.env` 文件中设置，参考 `.env.example`。

主要配置项：
- `API_HOST`, `API_PORT`: 服务监听地址和端口
- `MAX_IMAGE_SIZE`: 最大图像大小限制（字节）
- `SUPPORTED_FORMATS`: 支持的图像格式（逗号分隔）
- `ALLOWED_ORIGINS`: CORS允许的来源（逗号分隔）
- `LOG_LEVEL`: 日志级别（DEBUG/INFO/WARNING/ERROR）
- `LOG_FORMAT`: 日志格式（json/text）
- `CANNY_LOW_THRESHOLD`, `CANNY_HIGH_THRESHOLD`: 边缘检测阈值
- `CLAHE_CLIP_LIMIT`: 自适应直方图均衡化限制

## API 端点

- **GET /v1/health** - 健康检查
- **POST /v1/rectify** - 同步图像矫正
- **POST /v1/jobs** - 创建异步作业（占位实现）
- **GET /v1/jobs/{job_id}** - 查询作业状态（占位实现）

## 特性

✅ **已实现**：
- 结构化JSON日志，支持request_id追踪
- 基础图像处理流水线（文档检测+透视矫正+增强）
- 输入验证和错误处理
- 配置管理和环境变量支持
- Docker容器化部署
- 冒烟测试和基础测试框架

🔄 **开发中**：
- API认证机制
- 异步作业队列
- 更高级的弯曲矫正算法
- 性能监控和指标

## 开发指南

### 项目结构
```
├── app/
│   ├── main.py              # FastAPI应用入口
│   ├── config.py            # 配置管理
│   ├── logging.py           # 日志系统
│   ├── exceptions.py        # 异常定义
│   ├── validation.py        # 输入验证
│   ├── routers/
│   │   └── v1.py           # API路由
│   └── pipeline/
│       └── traditional.py  # 图像处理流水线
├── tests/                   # 测试文件
├── docs/                    # 文档
├── requirements.txt         # 依赖列表
├── .env.example            # 配置模板
├── start.py                # 启动脚本
└── Dockerfile              # Docker配置
```

### 添加新功能
1. 在相应模块中实现功能
2. 添加对应的测试
3. 更新配置和文档
4. 运行测试确保不破坏现有功能

