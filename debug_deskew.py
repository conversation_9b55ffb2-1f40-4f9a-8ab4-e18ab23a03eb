#!/usr/bin/env python3
"""
直接测试去倾斜算法的调试脚本
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.pipeline.traditional import run_pipeline, PipelineOptions, compute_skew_angle

def test_deskew_directly():
    """直接测试去倾斜算法"""
    print("🔍 直接测试去倾斜算法...")
    
    # 检查data目录
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ data/ 目录不存在")
        return
    
    # 查找图片文件
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    image_files = [f for f in data_dir.iterdir() 
                   if f.is_file() and f.suffix.lower() in image_extensions]
    
    if not image_files:
        print("❌ data/ 目录中没有找到图片文件")
        return
    
    print(f"📁 找到 {len(image_files)} 张图片")
    
    # 创建输出目录
    output_dir = Path("output_debug_deskew")
    output_dir.mkdir(exist_ok=True)
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n{'='*60}")
        print(f"📸 [{i}/{len(image_files)}] 测试: {image_file.name}")
        print(f"{'='*60}")
        
        try:
            # 读取图片
            with open(image_file, 'rb') as f:
                image_bytes = f.read()
            
            print(f"📁 原图路径: {image_file}")
            print(f"📊 文件大小: {len(image_bytes)} 字节")
            
            # 直接调用流水线
            options = PipelineOptions(
                brighten=True,
                denoise=True,
                sharpen=True,
                auto_deskew=True,
                deskew_max_angle=25.0
            )
            
            print(f"🔧 处理选项: auto_deskew={options.auto_deskew}, max_angle={options.deskew_max_angle}°")
            
            result = run_pipeline(image_bytes=image_bytes, options=options)
            
            print(f"✅ 处理完成:")
            print(f"   📍 检测分数: {result.score:.3f}")
            print(f"   🔄 旋转角度: {result.angle:.3f}°")
            print(f"   📐 图片尺寸: {result.size[0]}x{result.size[1]}")
            print(f"   ⏱️  处理时间: {result.timings.get('total_ms', 0):.1f}ms")
            
            # 保存结果
            if result.rectified_b64:
                import base64
                output_file = output_dir / f"debug_deskew_{image_file.stem}.jpg"
                image_data = base64.b64decode(result.rectified_b64)
                with open(output_file, 'wb') as f:
                    f.write(image_data)
                print(f"   💾 已保存: {output_file}")
            
            # 单独测试角度检测
            print(f"\n🔍 单独测试角度检测算法:")
            img = cv2.imdecode(np.frombuffer(image_bytes, np.uint8), cv2.IMREAD_COLOR)
            if img is not None:
                angle = compute_skew_angle(img, max_angle=25.0)
                print(f"   🎯 直接调用compute_skew_angle结果: {angle:.3f}°")
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 调试测试完成！输出目录: {output_dir.absolute()}")

if __name__ == "__main__":
    test_deskew_directly()
